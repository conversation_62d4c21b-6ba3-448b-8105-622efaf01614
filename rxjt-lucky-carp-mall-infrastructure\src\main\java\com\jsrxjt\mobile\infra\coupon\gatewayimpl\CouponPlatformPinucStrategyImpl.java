package com.jsrxjt.mobile.infra.coupon.gatewayimpl;

import cn.hutool.core.collection.CollectionUtil;
import com.coupon.rep.common.CouponResult;
import com.coupon.rep.common.PageInfo;
import com.coupon.rep.pinnuo.PnCouponInfo;
import com.coupon.rep.pinnuo.PnCreateOrderResult;
import com.coupon.req.config.CouponConfig;
import com.coupon.req.domain.common.CouponPageQueryBaseRequest;
import com.coupon.req.domain.pinnuo.PnCouponCreateOrderRequest;
import com.coupon.req.domain.pinnuo.PnCouponOrderDetailRequest;
import com.coupon.req.service.CouponService;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.coupon.annotation.CouponTypeHandler;
import com.jsrxjt.mobile.api.coupon.dto.response.CouponCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.coupon.types.CouponTypeEnum;
import com.jsrxjt.mobile.domain.coupon.entity.CouponCardEntity;
import com.jsrxjt.mobile.domain.coupon.entity.CouponGoodsSkuEntity;
import com.jsrxjt.mobile.domain.coupon.entity.CouponPlatformOrderDetail;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformStrategy;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.entity.SubSkuOrderEntity;
import com.jsrxjt.mobile.infra.coupon.utils.CouponPlatformSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 卡管品诺卡券
 * <AUTHOR>
 * @date 2025/03/15
 */
@Component
@CouponTypeHandler({CouponTypeEnum.PINUC})
@RequiredArgsConstructor
@Slf4j
public class CouponPlatformPinucStrategyImpl implements CouponPlatformStrategy {

    private final CouponService couponPlatformService;

    private final CouponConfig couponConfig;

    @Value("${pinuo.order.notify.url:}")
    private String notifyUrl;

    @Value("${pinuo.package.order.notify.url:}")
    private String packageNotifyUrl;

    @Override
    public List<CouponGoodsSkuEntity> getAllCouponGoodsSku() {
        CouponPageQueryBaseRequest request = new CouponPageQueryBaseRequest();
        int page = 1;
        request.setPage(page);
        request.setSize(20);
        List<CouponGoodsSkuEntity> allCouponGoodsSkuEntity = new ArrayList<>();
        List<PnCouponInfo> pnCouponInfoList = new ArrayList<>();
        log.info("开始获取卡管品诺卡券");
        do {
            //间隔1000毫秒 不然会提示限流
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            CouponResult couponResult = couponPlatformService.pnCouponsPage(request);
            if (couponResult != null && couponResult.getCode() == 0 && couponResult.getData() != null) {
                PageInfo<PnCouponInfo> data = (PageInfo<PnCouponInfo>) couponResult.getData();
                pnCouponInfoList = data.getList();
                if (CollectionUtil.isNotEmpty(pnCouponInfoList)) {
                    for (PnCouponInfo pnCouponInfo : pnCouponInfoList) {
                        allCouponGoodsSkuEntity.add(fillCouponGoodsSkuEntity(pnCouponInfo));
                    }
                    page++;
                    request.setPage(page);
                    log.info("成功获取第{}页卡管品诺卡券，本页获取数量：{}", page - 1, pnCouponInfoList.size());
                } else {
                    log.info("第{}页没有获取到卡管品诺卡券", page);
                    break;
                }
            } else {
                log.error("获取卡管品诺卡券失败，返回结果：{}", couponResult);
                throw new BizException("获取卡管品诺卡券失败");
            }
        } while (CollectionUtil.isNotEmpty(pnCouponInfoList));

        log.info("结束获取卡管品诺卡券，总获取数量：{}", allCouponGoodsSkuEntity.size());
        return allCouponGoodsSkuEntity;
    }

    @Override
    public CouponCreateOrderResponseDTO pushCouponOrder(OrderInfoEntity orderInfo) {
        OrderItemEntity orderItemEntity = orderInfo.getOrderItems().get(0);
        PnCouponCreateOrderRequest request = new PnCouponCreateOrderRequest();
        request.setAccount(orderInfo.getRechargeAccount() == null ? orderInfo.getCustomerMobile() : orderInfo.getRechargeAccount());
        request.setGoodsId(Long.valueOf(orderItemEntity.getOutGoodsId()));
        request.setNum(orderItemEntity.getQuantity());
        request.setThirdOrderSn(orderInfo.getOrderNo());
        request.setNotifyUrl(notifyUrl);
        CouponResult<PnCreateOrderResult> pnOrder = couponPlatformService.createPnOrder(request);
        CouponCreateOrderResponseDTO response = new CouponCreateOrderResponseDTO();
        if (pnOrder != null && pnOrder.getCode() == 0 && pnOrder.getData() != null) {
            log.info("创建卡管品诺订单成功：返回品诺订单号 {}", pnOrder.getData());
            response.setExternalOrderNo(pnOrder.getData().getOrderSn());
        } else if (pnOrder != null && pnOrder.getCode() != null && pnOrder.getCode() != 0) {
            log.error("创建卡管品诺订单失败，返回结果：{}", pnOrder);
            response.setCode(pnOrder.getCode());
            response.setMsg(pnOrder.getMsg()== null ? "" : pnOrder.getMsg());
        } else {
            log.error("创建卡管品诺订单接口异常，返回结果：{}", pnOrder);
            throw new BizException("创建卡管品诺订单失败");
        }

        return response;
    }

    @Override
    public CouponCreateOrderResponseDTO pushSubCouponOrder(SubSkuOrderEntity subSkuOrder) {
        PnCouponCreateOrderRequest request = new PnCouponCreateOrderRequest();
        request.setAccount(subSkuOrder.getRechargeAccount());
        request.setGoodsId(Long.valueOf(subSkuOrder.getOuterId()));
        request.setNum(subSkuOrder.getQuantity());
        request.setThirdOrderSn(subSkuOrder.getSubOrderNo());
        request.setNotifyUrl(packageNotifyUrl);
        CouponResult<PnCreateOrderResult> pnOrder = couponPlatformService.createPnOrder(request);
        CouponCreateOrderResponseDTO response = new CouponCreateOrderResponseDTO();
        if (pnOrder != null && pnOrder.getCode() == 0 && pnOrder.getData() != null) {
            log.info("创建套餐子产品品诺订单成功：返回品诺订单号 {}", pnOrder.getData());
            response.setExternalOrderNo(pnOrder.getData().getOrderSn());
        } else {
            log.error("创建套餐子产品品诺订单失败，返回结果：{}", pnOrder);
            throw new BizException("创建套餐子产品品诺订单失败");
        }

        return response;
    }

    @Override
    public List<CouponCardEntity> getCouponCardInfo(String externalOrderNo) {
        throw new UnsupportedOperationException("不支持获取卡管品诺的具体卡信息");
    }

    @Override
    public CouponPlatformOrderDetail getCouponPlatformOrderDetail(String orderNo) {
        PnCouponOrderDetailRequest request = new PnCouponOrderDetailRequest();
        request.setThirdOrderSn(orderNo);

        //couponPlatformService.getPnOrderDetail(request)

        return null;
    }

    @Override
    public String getParametersSign(Map<String, Object> params) {
        return CouponPlatformSignUtil.signStr(params,couponConfig.getAppSecret());
    }

    @Override
    public Integer getInventory(String couponId) {
        throw new UnsupportedOperationException("不支持获取卡管品诺的库存");
    }

    private CouponGoodsSkuEntity fillCouponGoodsSkuEntity(PnCouponInfo pnCouponInfo){
        CouponGoodsSkuEntity couponGoodsSkuEntity = new CouponGoodsSkuEntity();
        couponGoodsSkuEntity.setCouponPlatformId(String.valueOf(pnCouponInfo.getGoodsId()));
        if (pnCouponInfo.getAmount() != null){
            couponGoodsSkuEntity.setAmount(new BigDecimal(pnCouponInfo.getAmount()).setScale(2,BigDecimal.ROUND_HALF_UP));
        }
        if (pnCouponInfo.getSettlePrice() != null){
            couponGoodsSkuEntity.setCostPrice(new BigDecimal(pnCouponInfo.getSettlePrice()).setScale(2,BigDecimal.ROUND_HALF_UP));
        }
        if (pnCouponInfo.getPrice() != null){
            couponGoodsSkuEntity.setPrice(new BigDecimal(pnCouponInfo.getPrice()).setScale(2,BigDecimal.ROUND_HALF_UP));
        }
        if (pnCouponInfo.getStatus() != null){
            couponGoodsSkuEntity.setCenterStatus(pnCouponInfo.getStatus());
        }
        if (pnCouponInfo.getBuyLimit() != null){
            couponGoodsSkuEntity.setRationSaleNum(pnCouponInfo.getBuyLimit());
        }
        Integer accountType = pnCouponInfo.getAccountType();
        if (accountType != null){
            couponGoodsSkuEntity.setAccountType(accountType);
        }
        //品诺类型
        Integer type = pnCouponInfo.getType();
        if (type != null){
            couponGoodsSkuEntity.setPnType(type);
        }

        return couponGoodsSkuEntity;
    }
}
