package com.jsrxjt.mobile.biz.payment.strategy.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.coupon.dto.response.CouponCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.coupon.types.CouponTypeEnum;
import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.biz.payment.strategy.PaymentSuccessStrategy;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformFactory;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformStrategy;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 卡券类型支付成功处理策略（基于flatProductType）
 * 支持 flatProductType / 100 = 1 的所有卡券类型
 * 
 * <AUTHOR> Fengping
 * @since 2025/1/27
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CouponPaymentSuccessStrategy implements PaymentSuccessStrategy {

    private final OrderRepository orderRepository;

    @Override
    public boolean supports(Integer flatProductType) {
        // 支持 flatProductType / 100 = 1 的所有卡券类型
        // 例如：101(普通卡券)、102(视频直充)、103(品诺卡券)、104(品诺直充)
        return flatProductType != null && flatProductType / 100 == 1;
    }

    @Override
    public void handle(OrderInfoEntity order) {
        log.info("开始处理卡券订单支付成功，订单号：{}，扁平化产品类型：{}",
                order.getOrderNo(), order.getFlatProductType());

        // 1. 推单到卡管
        String externalOrderNo = pushCouponOrder(order);

        // 2. 更新订单状态为发货中，并记录外部订单号
        updateOrderToDelivering(order, externalOrderNo);

        log.info("卡券订单支付成功处理完成，订单号：{}，外部订单号：{}",
                order.getOrderNo(), externalOrderNo);
    }

    /**
     * 推单到卡管
     */
    private String pushCouponOrder(OrderInfoEntity order) {
        // 根据flatProductType mod 100 得到具体卡券的类型
        int couponTypeCode = order.getFlatProductType() % 100;
        CouponTypeEnum couponType = CouponTypeEnum.getByType(couponTypeCode);

        if (couponType == null) {
            log.error("无法识别的卡券类型，订单号：{}，扁平化产品类型：{}，卡券类型码：{}",
                    order.getOrderNo(), order.getFlatProductType(), couponTypeCode);
            throw new BizException("无法识别的卡券类型，卡券类型码：" + couponTypeCode);
        }

        log.info("识别卡券类型成功，订单号：{}，扁平化产品类型：{}，卡券类型：{}",
                order.getOrderNo(), order.getFlatProductType(), couponType.getText());

        // 获取对应的卡券平台策略并推单
        CouponPlatformStrategy couponPlatform = CouponPlatformFactory.getCouponPlatform(couponType);
        CouponCreateOrderResponseDTO pushResult = couponPlatform.pushCouponOrder(order);

        if (pushResult == null || pushResult.getExternalOrderNo() == null) {
            log.error("卡券推单失败，订单号：{}，扁平化产品类型：{}",
                    order.getOrderNo(), order.getFlatProductType());
            throw new BizException("卡券推单失败");
        }

        log.info("卡券推单成功，订单号：{}，扁平化产品类型：{}，外部订单号：{}",
                order.getOrderNo(), order.getFlatProductType(), pushResult.getExternalOrderNo());

        return pushResult.getExternalOrderNo();
    }

    /**
     * 更新订单状态为发货中
     */
    private void updateOrderToDelivering(OrderInfoEntity order, String externalOrderNo) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setExternalOrderNo(externalOrderNo);
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERING.getCode().intValue());
        updateOrder.setModTime(LocalDateTime.now());

        orderRepository.updateOrder(updateOrder);

        log.info("卡券订单发货状态更新成功，订单号：{}，发货状态：{}，外部订单号：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERING.getDescription(), externalOrderNo);
    }
}