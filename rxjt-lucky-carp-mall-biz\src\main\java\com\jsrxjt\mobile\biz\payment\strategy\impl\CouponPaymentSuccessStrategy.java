package com.jsrxjt.mobile.biz.payment.strategy.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.coupon.dto.response.CouponCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.coupon.types.CouponTypeEnum;
import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.biz.payment.strategy.PaymentSuccessStrategy;
import com.jsrxjt.mobile.domain.coupon.entity.CouponGoodsSkuEntity;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformFactory;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformStrategy;
import com.jsrxjt.mobile.domain.coupon.repository.CouponGoodsSkuRepository;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 卡券类型支付成功处理策略（基于flatProductType）
 * 支持 flatProductType / 100 = 1 的所有卡券类型
 * 
 * <AUTHOR> Fengping
 * @since 2025/1/27
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CouponPaymentSuccessStrategy implements PaymentSuccessStrategy {

    private final OrderRepository orderRepository;
    private final CouponGoodsSkuRepository couponGoodsSkuRepository;

    @Override
    public boolean supports(Integer flatProductType) {
        // 支持 flatProductType / 100 = 1 的所有卡券类型
        // 例如：101(普通卡券)、102(视频直充)、103(品诺卡券)、104(品诺直充)
        return flatProductType != null && flatProductType / 100 == 1;
    }

    @Override
    public void handle(OrderInfoEntity order) {
        log.info("开始处理卡券订单支付成功，订单号：{}，扁平化产品类型：{}",
                order.getOrderNo(), order.getFlatProductType());

        // 1. 推单到卡管
        String externalOrderNo = pushCouponOrder(order);

        // 2. 更新订单状态为发货中，并记录外部订单号
        updateOrderToDelivering(order, externalOrderNo);

        // 3. 更新卡券SKU销量
        updateCouponSkuSoldNum(order);

        log.info("卡券订单支付成功处理完成，订单号：{}，外部订单号：{}",
                order.getOrderNo(), externalOrderNo);
    }

    /**
     * 推单到卡管
     */
    private String pushCouponOrder(OrderInfoEntity order) {
        // 根据flatProductType mod 100 得到具体卡券的类型
        int couponTypeCode = order.getFlatProductType() % 100;
        CouponTypeEnum couponType = CouponTypeEnum.getByType(couponTypeCode);

        if (couponType == null) {
            log.error("无法识别的卡券类型，订单号：{}，扁平化产品类型：{}，卡券类型码：{}",
                    order.getOrderNo(), order.getFlatProductType(), couponTypeCode);
            throw new BizException("无法识别的卡券类型，卡券类型码：" + couponTypeCode);
        }

        log.info("识别卡券类型成功，订单号：{}，扁平化产品类型：{}，卡券类型：{}",
                order.getOrderNo(), order.getFlatProductType(), couponType.getText());

        // 获取对应的卡券平台策略并推单
        CouponPlatformStrategy couponPlatform = CouponPlatformFactory.getCouponPlatform(couponType);
        CouponCreateOrderResponseDTO pushResult = couponPlatform.pushCouponOrder(order);

        if (pushResult == null || pushResult.getExternalOrderNo() == null) {
            log.error("卡券推单失败，订单号：{}，扁平化产品类型：{}",
                    order.getOrderNo(), order.getFlatProductType());
            throw new BizException("卡券推单失败");
        }

        log.info("卡券推单成功，订单号：{}，扁平化产品类型：{}，外部订单号：{}",
                order.getOrderNo(), order.getFlatProductType(), pushResult.getExternalOrderNo());

        return pushResult.getExternalOrderNo();
    }

    /**
     * 更新订单状态为发货中
     */
    private void updateOrderToDelivering(OrderInfoEntity order, String externalOrderNo) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setExternalOrderNo(externalOrderNo);
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERING.getCode().intValue());
        updateOrder.setModTime(LocalDateTime.now());

        orderRepository.updateOrder(updateOrder);

        log.info("卡券订单发货状态更新成功，订单号：{}，发货状态：{}，外部订单号：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERING.getDescription(), externalOrderNo);
    }

    /**
     * 更新卡券SKU销量
     */
    private void updateCouponSkuSoldNum(OrderInfoEntity order) {
        try {
            // 获取订单中的商品SKU ID和数量
            Long productSkuId = order.getProductSkuId();
            Integer quantity = 1; // 默认数量为1

            // 如果订单有订单项，从订单项中获取数量
            if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
                OrderItemEntity orderItem = order.getOrderItems().get(0);
                quantity = orderItem.getQuantity();
                // 如果订单主表的productSkuId为空，从订单项中获取
                if (productSkuId == null) {
                    productSkuId = orderItem.getProductSkuId();
                }
            }

            if (productSkuId == null) {
                log.warn("订单商品SKU ID为空，无法更新销量，订单号：{}", order.getOrderNo());
                return;
            }

            // 查询当前SKU信息
            CouponGoodsSkuEntity currentSku = couponGoodsSkuRepository.getCouponGoodsSkuById(productSkuId);
            if (currentSku == null) {
                log.warn("未找到对应的卡券SKU，SKU ID：{}，订单号：{}", productSkuId, order.getOrderNo());
                return;
            }

            // 更新销量
            CouponGoodsSkuEntity updateSku = new CouponGoodsSkuEntity();
            updateSku.setCouponSkuId(productSkuId);
            updateSku.setSoldNum((currentSku.getSoldNum() == null ? 0 : currentSku.getSoldNum()) + quantity);

            int updateResult = couponGoodsSkuRepository.updateCouponGoodsSku(updateSku);
            if (updateResult > 0) {
                log.info("卡券SKU销量更新成功，SKU ID：{}，增加数量：{}，当前销量：{}，订单号：{}",
                        productSkuId, quantity, updateSku.getSoldNum(), order.getOrderNo());
            } else {
                log.warn("卡券SKU销量更新失败，SKU ID：{}，订单号：{}", productSkuId, order.getOrderNo());
            }

        } catch (Exception e) {
            log.error("更新卡券SKU销量异常，订单号：{}，错误信息：{}", order.getOrderNo(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }
}