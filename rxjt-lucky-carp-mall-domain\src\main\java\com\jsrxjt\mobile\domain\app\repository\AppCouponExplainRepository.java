package com.jsrxjt.mobile.domain.app.repository;


import com.jsrxjt.mobile.api.app.types.AppCouponExplainTypeEnum;
import com.jsrxjt.mobile.domain.app.entity.AppCouponExplainEntity;

import java.util.List;

public interface AppCouponExplainRepository {

    /**
     * 根据spuId和类型查询说明
     */
   List<AppCouponExplainEntity> getExplainBySpuIdAndType(Long spuId,Integer appType);


    /**
     * 根据spuId、说明类型查询说明信息
     *
     * @param spuId spuId
     * @param explainType 说明类型
     * @return 说明信息
     */
    AppCouponExplainEntity getBySpuIdAndExplainType(Long spuId, AppCouponExplainTypeEnum explainType);

}
