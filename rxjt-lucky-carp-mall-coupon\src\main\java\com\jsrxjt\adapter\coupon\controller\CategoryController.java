package com.jsrxjt.adapter.coupon.controller;

import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.product.dto.ProductBaseInfoDTO;
import com.jsrxjt.mobile.api.product.dto.ProductCategoryDTO;
import com.jsrxjt.mobile.api.product.dto.request.CategoryDetailRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.MultiCategoryRequestDTO;
import com.jsrxjt.mobile.api.product.dto.response.CategorySearchResponseDTO;
import com.jsrxjt.mobile.biz.product.service.ProductCategoryCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分类页相关接口
 * 
 * <AUTHOR> Fengping
 * @since 2025/5/12
 **/
@RestController
@RequestMapping("/v1/category")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "分类接口", description = "分类接口")
public class CategoryController {

    private final ProductCategoryCaseService productCategoryCaseService;

    /**
     * 获取一级分类列表
     *
     * @return 一级分类列表
     */
    @GetMapping("/first-level")
    @Operation(summary = "获取一级分类列表", description = "获取所有启用状态的一级产品分类，按排序值降序排列")
    public BaseResponse<List<ProductCategoryDTO>> getFirstLevelCategories() {
        return BaseResponse.succeed(productCategoryCaseService.getFirstLevelCategories());
    }

    /**
     * 根据一级分类ID获取二级分类详情
     *
     * @param request 请求参数，包含一级分类ID
     * @return 二级分类详情列表，包括分类信息、广告位和产品列表
     */
    @PostMapping("/second-level/details")
    @Operation(summary = "获取二级分类详情", description = "根据一级分类ID获取二级分类的名称、广告位和产品列表")
    @VerifySign
    public BaseResponse<CategorySearchResponseDTO> getSecondLevelCategoryDetails(
            @RequestBody @Validated CategoryDetailRequestDTO request) {
        log.info("接收到获取二级分类详情请求，一级分类ID：{}", request.getParentCategoryId());
        CategorySearchResponseDTO  details = productCategoryCaseService
                .getSecondLevelCategoryDetails(request);
        return BaseResponse.succeed(details);
    }

    /**
     * 根据二级分类ID列表获取产品列表
     *
     * @param request 请求参数，包含分类ID列表
     * @return 产品列表
     */
    @PostMapping("/second-levels-products")
    @Operation(summary = "根据二级分类获取产品列表", description = "根据分类ID列表获取产品列表")
    @VerifySign
    public BaseResponse<List<ProductBaseInfoDTO>> findProductsByCategoryIds(
            @RequestBody @Validated MultiCategoryRequestDTO  request) {
        return BaseResponse.succeed(productCategoryCaseService.findProductsByCategoryIds(request));
    }
}
