package com.jsrxjt.mobile.biz.ticket;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.ticket.TicketDeliveryDetailResponseDTO;
import com.jsrxjt.mobile.api.ticket.TicketDeliveryResponseDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketDeliveryDetailRequestDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketDeliveryRequestDTO;
import com.jsrxjt.mobile.api.ticket.TicketShopListResponseDTO;

public interface TicketDeliveryService {
    PageDTO<TicketDeliveryResponseDTO> getTicketDelivery(TicketDeliveryRequestDTO  request);

    TicketDeliveryDetailResponseDTO getTicketDeliveryById(TicketDeliveryDetailRequestDTO request);

    void delTicketDelivery(TicketDeliveryDetailRequestDTO request);

    // 门店优惠券列表
    PageDTO<TicketShopListResponseDTO> getShopTicketList(TicketDeliveryRequestDTO request);

}
