package com.jsrxjt.adapter.coupon.controller;


import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.app.request.AlipayAppPreOrderRequest;
import com.jsrxjt.mobile.api.app.request.AlipayAppRequest;
import com.jsrxjt.mobile.api.app.request.AlipayVoucherLastAccountRequest;
import com.jsrxjt.mobile.api.app.request.AlipayVoucherRechargeRecordRequest;
import com.jsrxjt.mobile.api.app.response.AlipayAppCatResponse;
import com.jsrxjt.mobile.api.app.response.AlipayAppResponse;
import com.jsrxjt.mobile.api.app.response.AlipayVoucherRechargeRecordResponse;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.biz.app.AlipayAppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/v1/app")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "支付宝红包充值页", description = "支付宝红包充值页")
public class AppCouponGoodsController {

    private final AlipayAppService alipayAppService;

    /**
     * 获取支付宝红包分类
     * @return
     */
    @GetMapping("/alipay/cats")
    @Operation(summary = "获取支付宝红包分类", description = "获取支付宝红包分类")
    BaseResponse<List<AlipayAppCatResponse>> getAlipayAppCats(){
        return BaseResponse.succeed(alipayAppService.getAlipayAppCats());
    }

    /**
     * 获取支付宝红包详情信息
     * @param request
     * @return
     */
    @PostMapping("/alipay/info")
    @Operation(summary = "获取支付宝红包详情信息", description = "获取支付宝红包详情信息")
    BaseResponse<AlipayAppResponse> getAlipayAppInfo(@RequestBody @Valid AlipayAppRequest request){
        return BaseResponse.succeed(alipayAppService.getAlipayAppInfo(request));
    }

//    /**
//     * 支付宝红包预充值是否满足条件
//     * @param request
//     * @return
//     */
//    @PostMapping("/alipay/immediatePayment")
//    @Operation(summary = "立即充值判断", description = "支付宝红包预充值是否满足条件")
//    BaseResponse<Boolean> checkImmediatePayment(@RequestBody @Valid AlipayAppPreOrderRequest request){
//        return BaseResponse.succeed(alipayAppService.checkImmediatePayment(request));
//    }

    /*@PostMapping("/alipay/rechargeRecords")
    @Operation(summary = "分页查询支付宝充值记录")
    @VerifySign(hasToken = true)
    public BaseResponse<PageDTO<AlipayVoucherRechargeRecordResponse>> rechargeRecords(@RequestBody @Valid AlipayVoucherRechargeRecordRequest request) {
        return BaseResponse.succeed(alipayAppService.getRechargeRecords(request));
    }*/

    @PostMapping("/alipay/getLastRechargeAccount")
    @Operation(summary = "获取上次充值账号")
    @VerifySign(hasToken = true)
    public BaseResponse<String> getLastRechargeAccount(@RequestBody @Valid AlipayVoucherLastAccountRequest request) {
        return BaseResponse.succeed(alipayAppService.getLastRechargeAccount(request.getCustomerId()));
    }

}
