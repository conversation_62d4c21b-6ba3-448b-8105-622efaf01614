package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Description:购券明细
 **/
@Data
public class SelCouponCodeDetailResponseDTO {

    @Schema(description = "url")
    private String couponUrl;

    @Schema(description = "卡号")
    private String couponCode;

    @Schema(description = "卡名称")
    private String couponName;

    @Schema(description = "下单日期")
    private Date orderDate;

    @Schema(description = "有效期")
    private String validTime;

    @Schema(description = "余额")
    private String balance;//卡余额 单位分

    @Schema(description = "卡状态 EXPIRED-已过期 USED-已使用 USABLE-能使用 INACTIVE-未激活")
    private String cardStatus;


}
