package com.jsrxjt.mobile.infra.coupon.gatewayimpl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.coupon.rep.common.CouponResult;
import com.coupon.rep.common.PageInfo;
import com.coupon.rep.coupon.CouponCreateOrderResult;
import com.coupon.rep.coupon.CouponGetCard;
import com.coupon.rep.coupon.CouponInfo;
import com.coupon.rep.coupon.CouponStock;
import com.coupon.req.config.CouponConfig;
import com.coupon.req.domain.coupon.CouponsFindRequest;
import com.coupon.req.domain.coupon.CreateCouponOrderRequest;
import com.coupon.req.service.CouponService;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.coupon.annotation.CouponTypeHandler;
import com.jsrxjt.mobile.api.coupon.dto.response.CouponCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.coupon.types.CouponTypeEnum;
import com.jsrxjt.mobile.domain.coupon.entity.CouponCardEntity;
import com.jsrxjt.mobile.domain.coupon.entity.CouponGoodsSkuEntity;
import com.jsrxjt.mobile.domain.coupon.entity.CouponPlatformOrderDetail;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformStrategy;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.entity.SubSkuOrderEntity;
import com.jsrxjt.mobile.infra.coupon.utils.CouponPlatformSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 卡管普通卡券
 * 
 * <AUTHOR>
 * @date 2025/03/15
 */
@Component
@CouponTypeHandler({ CouponTypeEnum.REGULAR })
@RequiredArgsConstructor
@Slf4j
public class CouponPlatformStrategyImpl implements CouponPlatformStrategy {

    private final CouponService couponPlatformService;

    private final CouponConfig couponConfig;

    @Value("${coupon.order.notify.url:}")
    private String notifyUrl;

    @Value("${package.order.notify.url:}")
    private String packageNotifyUrl;

    @Override
    public List<CouponGoodsSkuEntity> getAllCouponGoodsSku() {
        CouponsFindRequest request = new CouponsFindRequest();
        int page = 1;
        request.setPage(page);
        request.setSize(100);
        List<CouponGoodsSkuEntity> allCouponGoodsSkuEntity = new ArrayList<>();
        List<CouponInfo> couponInfoList = new ArrayList<>();
        log.info("开始获取卡管卡券");
        do {
            //间隔1000毫秒 不然会提示限流
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            CouponResult couponResult = couponPlatformService.findCouponList(request);
            if (couponResult != null && couponResult.getCode() == 0 && couponResult.getData() != null) {
                PageInfo<CouponInfo> data = (PageInfo<CouponInfo>) couponResult.getData();
                couponInfoList = data.getList();
                if (CollectionUtil.isNotEmpty(couponInfoList)) {
                    for (CouponInfo couponInfo : couponInfoList) {
                        allCouponGoodsSkuEntity.add(fillCouponGoodsSkuEntity(couponInfo));
                    }
                    page++;
                    request.setPage(page);
                    log.info("成功获取第{}页卡管卡券，本页获取数量：{}", page - 1, couponInfoList.size());
                } else {
                    log.info("第{}页没有获取到卡管卡券", page);
                    break;
                }
            } else {
                log.error("获取卡管卡券失败，返回结果：{}", couponResult);
                throw new BizException("获取卡管卡券失败");
            }
        } while (CollectionUtil.isNotEmpty(couponInfoList));
        log.info("结束获取卡管卡券，总获取数量：{}", allCouponGoodsSkuEntity.size());
        return allCouponGoodsSkuEntity;
    }

    @Override
    public CouponCreateOrderResponseDTO pushCouponOrder(OrderInfoEntity orderInfo) {
        OrderItemEntity orderItem = orderInfo.getOrderItems().get(0);
        CreateCouponOrderRequest request = new CreateCouponOrderRequest();
        request.setOutOrderSn(orderInfo.getOrderNo());
        request.setOutUserSn(String.valueOf(orderInfo.getCustomerId()));
        request.setNum(orderItem.getQuantity());
        request.setOutMobile(orderInfo.getCustomerMobile());
        request.setCouponId(Long.valueOf(orderItem.getOutGoodsId()));
        request.setNotifyUrl(notifyUrl);
        log.info("开始创建卡管卡券订单，请求参数：{}", JSON.toJSONString(request));

        CouponResult<CouponCreateOrderResult> couponOrder = couponPlatformService.createCouponOrder(request);

        CouponCreateOrderResponseDTO responseDTO = new CouponCreateOrderResponseDTO();
        if (couponOrder != null && couponOrder.getCode() == 0 && couponOrder.getData() != null) {
            log.info("创建卡管卡券订单成功：{}", couponOrder);
            CouponCreateOrderResult data = couponOrder.getData();
            responseDTO.setOrderNo(data.getOutOrderSn());
            responseDTO.setExternalOrderNo(data.getOrderSn());
            responseDTO.setTotalAmount(new BigDecimal(data.getTotalPrice()));
            responseDTO.setTotalSettlePrice(new BigDecimal(data.getTotalSettlePrice()));
            responseDTO.setCode(0);

        } else if (couponOrder != null && couponOrder.getCode() != null && couponOrder.getCode() != 0) {
            //接口有错误码返回
            log.error("创建卡管卡券订单失败，返回结果：{}", JSON.toJSONString(couponOrder));
            responseDTO.setCode(couponOrder.getCode());
            responseDTO.setMsg(couponOrder.getMsg()== null ? "" : couponOrder.getMsg());
        } else {
            log.error("创建卡管卡券订单异常，返回结果：{}", JSON.toJSONString(couponOrder));
            throw new BizException("创建卡管卡券订单失败");
        }
        return responseDTO;
    }

    @Override
    public CouponCreateOrderResponseDTO pushSubCouponOrder(SubSkuOrderEntity subSkuOrder) {
        CreateCouponOrderRequest request = new CreateCouponOrderRequest();
        request.setOutOrderSn(subSkuOrder.getSubOrderNo());
        request.setOutUserSn(String.valueOf(subSkuOrder.getCustomerId()));
        request.setNum(subSkuOrder.getQuantity());
        request.setOutMobile(subSkuOrder.getRechargeAccount());
        request.setCouponId(Long.valueOf(subSkuOrder.getOuterId()));
        request.setNotifyUrl(packageNotifyUrl);
        log.info("开始创建套餐的子商品卡管卡券订单，请求参数：{}", JSON.toJSONString(request));

        CouponResult<CouponCreateOrderResult> couponOrder = couponPlatformService.createCouponOrder(request);

        CouponCreateOrderResponseDTO responseDTO = new CouponCreateOrderResponseDTO();
        if (couponOrder != null && couponOrder.getCode() == 0 && couponOrder.getData() != null) {
            log.info("创建套餐的卡管卡券订单成功：{}", couponOrder);
            CouponCreateOrderResult data = couponOrder.getData();
            responseDTO.setOrderNo(data.getOutOrderSn());
            responseDTO.setExternalOrderNo(data.getOrderSn());
            responseDTO.setTotalAmount(new BigDecimal(data.getTotalPrice()));
            responseDTO.setTotalSettlePrice(new BigDecimal(data.getTotalSettlePrice()));

        } else {
            log.error("创建套餐的卡管卡券订单失败，返回结果：{}", JSON.toJSONString(couponOrder));
            throw new BizException("创建套餐的卡管卡券订单失败");
        }
        return responseDTO;
    }

    @Override
    public List<CouponCardEntity> getCouponCardInfo(String externalOrderNo) {
        List<CouponCardEntity> cards = new ArrayList<>();
        log.info("开始获取卡管卡券信息，外部订单号：{}", externalOrderNo);
        CouponResult<List<CouponGetCard>> couponResult = couponPlatformService.getCard(externalOrderNo);
        if (couponResult != null && couponResult.getCode() == 0 && couponResult.getData() != null) {
            log.info("获取卡管卡券成功，返回结果：{}", JSON.toJSONString(couponResult));
            // 将CouponGetCard转换为CouponCardEntity
            for (CouponGetCard couponGetCard : couponResult.getData()) {
                CouponCardEntity cardEntity = new CouponCardEntity();

                // 基本信息映射
                cardEntity.setId(couponGetCard.getId());
                cardEntity.setCardCode(couponGetCard.getCode());
                cardEntity.setCardPass(couponGetCard.getPass());
                cardEntity.setCrc(couponGetCard.getCrc());
                cardEntity.setShortUrl(couponGetCard.getUrl());
                cardEntity.setShortUrlPass(couponGetCard.getUrlPass());
                cardEntity.setType(Integer.valueOf(couponGetCard.getType()));
                cardEntity.setBatchNum(couponGetCard.getBatchNum());
                if (StringUtils.isNotBlank(couponGetCard.getValidTime())) {
                    cardEntity.setValidTime(LocalDate.parse(couponGetCard.getValidTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                }
                cardEntity.setH5Url(couponGetCard.getH5Url());
                cardEntity.setCouponMemberId(couponGetCard.getCouponMemberId());

                cards.add(cardEntity);
            }
        } else {
            log.error("获取卡管卡券失败，返回结果：{}", JSON.toJSONString(couponResult));
            throw new BizException("获取卡管卡券失败");
        }
        return cards;
    }

    @Override
    public CouponPlatformOrderDetail getCouponPlatformOrderDetail(String orderNo) {
        return null;
    }

    @Override
    public String getParametersSign(Map<String, Object> params) {
        return CouponPlatformSignUtil.signStr(params,couponConfig.getAppSecret());
    }

    @Override
    public Integer getInventory(String couponId) {
        log.info("开始获取卡券库存，中台卡券ID：{}", couponId);
        CouponResult<CouponStock> couponStockCouponResult = couponPlatformService.getCouponStock(Long.valueOf(couponId));
        if (couponStockCouponResult != null
                && couponStockCouponResult.getCode() == 0 && couponStockCouponResult.getData() != null) {
            log.info("获取卡券库存成功，返回结果：{}", JSON.toJSONString(couponStockCouponResult));
            return couponStockCouponResult.getData().getNum();
        } else if (couponStockCouponResult != null && couponStockCouponResult.getCode() != 0) {
            log.error("获取卡券库存失败，返回结果：{}", JSON.toJSONString(couponStockCouponResult));
            throw new BizException("获取卡券库存失败:" + couponStockCouponResult.getMsg());
        } else {
            log.error("获取卡券库存异常，返回结果：{}", JSON.toJSONString(couponStockCouponResult));
            throw new BizException("获取卡券库存失败");
        }
    }

    private CouponGoodsSkuEntity fillCouponGoodsSkuEntity(CouponInfo couponInfo) {
        CouponGoodsSkuEntity couponGoodsSkuEntity = new CouponGoodsSkuEntity();
        couponGoodsSkuEntity.setCouponPlatformId(String.valueOf(couponInfo.getId()));
        if (couponInfo.getAmount() != null) {
            couponGoodsSkuEntity
                    .setAmount(new BigDecimal(couponInfo.getAmount()).setScale(2, BigDecimal.ROUND_HALF_UP));
        }
        if (couponInfo.getSettlePrice() != null) {
            couponGoodsSkuEntity
                    .setCostPrice(new BigDecimal(couponInfo.getSettlePrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
        }
        if (couponInfo.getPrice() != null) {
            couponGoodsSkuEntity.setPrice(new BigDecimal(couponInfo.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
        }
        if (couponInfo.getSellStatus() != null) {
            // 卡管普通卡状态: 0上架 1下架
            couponGoodsSkuEntity.setCenterStatus(Math.abs(couponInfo.getSellStatus() - 1));
        }
        if (couponInfo.getStockNum() != null) {
            couponGoodsSkuEntity.setInventory(couponInfo.getStockNum());
        }
        return couponGoodsSkuEntity;
    }
}
