package com.jsrxjt.adapter.order.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.order.dto.request.*;
import com.jsrxjt.mobile.api.order.dto.response.GiftTicketResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.OrderCreatedDTO;
import com.jsrxjt.mobile.api.order.dto.response.OrderDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.OrderListResponseDTO;
import com.jsrxjt.mobile.biz.order.GiftTicketValidationService;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 普通订单Controller
 * 用于福鲤圈普通商品下单：卡券、套餐、支付宝话费等
 * 
 * <AUTHOR> Fengping
 * @since 2025/6/14
 */
@RestController
@RequestMapping("/v1/order")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "订单接口", description = "普通订单相关接口")
public class OrderController {

    private final OrderCaseService orderCaseService;

    private final GiftTicketValidationService giftTicketValidationService;

    /**
     * 福鲤圈普通商品下单
     */
    @PostMapping("/submit")
    @Operation(summary = "提交订单", description = "福鲤圈普通商品下单")
    @VerifySign(hasToken = true)
    public BaseResponse<OrderCreatedDTO> submitOrder(@RequestBody @Valid CreateStandardOrderRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        log.info("接收到普通订单提交请求，客户ID：{}", customerId);
        //下单
        CreateOrderDTO dto = new CreateOrderDTO();
        BeanUtils.copyProperties(request, dto);
        dto.setCustomerId(customerId);
        OrderInfoEntity order = orderCaseService.submitOrder(dto);
        OrderCreatedDTO result = new OrderCreatedDTO();
        BeanUtils.copyProperties(order, result);
        result.setGiftTicketList(convert2GiftTicketsDTO(order));
        return BaseResponse.succeed(result);
    }

    private  List<GiftTicketResponseDTO> convert2GiftTicketsDTO(OrderInfoEntity order) {
        if (!CollectionUtils.isEmpty(order.getGiftTickets())) {
            List<GiftTicketResponseDTO> giftTicketsDTO = order.getGiftTickets().stream()
                    .map(giftTicketInfo -> {
                        GiftTicketResponseDTO ticketResponseDTO = new GiftTicketResponseDTO();
                        ticketResponseDTO.setTicketId(giftTicketInfo.getTicketId());
                        ticketResponseDTO.setTicketName(giftTicketInfo.getTicketName());
                        ticketResponseDTO.setSpecPicUrl(giftTicketInfo.getSpecPicUrl());
                        ticketResponseDTO.setTicketNum(giftTicketInfo.getTicketNum());
                        return ticketResponseDTO;
                    })
                    .toList();
            return giftTicketsDTO;
        }
        return null;
    }

    /**
     * 取消订单
     */
    @PostMapping("/cancel")
    @Operation(summary = "取消订单", description = "取消待支付状态的订单")
    @VerifySign(hasToken = true)
    public BaseResponse<Void> cancelOrder(@RequestBody @Valid CancelOrderDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        log.info("接收到取消订单请求，客户ID：{}，订单号：{}", customerId, request.getOrderNo());
        
        orderCaseService.cancelOrder(request.getOrderNo(),customerId);
        
        log.info("订单取消成功，客户ID：{}，订单号：{}", customerId, request.getOrderNo());
        return BaseResponse.succeed();
    }

    /**
     * 订单列表分页查询
     */
    @PostMapping("/page")
    @Operation(summary = "订单列表", description = "分页查询用户订单列表")
    @VerifySign(hasToken = true)
    public BaseResponse<PageDTO<OrderListResponseDTO>> pageOrderList(@RequestBody @Valid OrderListRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        log.info("接收到订单列表查询请求，客户ID：{}，订单状态：{}", customerId, request.getOrderStatus());
        
        PageDTO<OrderListResponseDTO> result = orderCaseService.pageOrderList(customerId, request);
        
        return BaseResponse.succeed(result);
    }

    /**
     * 订单详情查询
     */
    @PostMapping("/detail")
    @Operation(summary = "订单详情", description = "根据订单号查询订单详情")
    @VerifySign(hasToken = true)
    public BaseResponse<OrderDetailResponseDTO> getOrderDetail(@RequestBody @Valid OrderDetailRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        log.info("接收到订单详情查询请求，客户ID：{}，订单号：{}", customerId, request.getOrderNo());
        
        OrderDetailResponseDTO result = orderCaseService.getOrderDetail(customerId, request.getOrderNo());

        
        return BaseResponse.succeed(result);
    }

    /**
     * 删除订单
     */
    @PostMapping("/delete")
    @Operation(summary = "删除订单", description = "删除订单（设置为不显示状态）")
    @VerifySign(hasToken = true)
    public BaseResponse<Void> deleteOrder(@RequestBody @Valid DeleteOrderRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        log.info("接收到删除订单请求，客户ID：{}，订单号：{}", customerId, request.getOrderNo());
        
        orderCaseService.markOrderAsHidden(request.getOrderNo(), customerId);
        
        log.info("订单删除成功，客户ID：{}，订单号：{}", customerId, request.getOrderNo());
        return BaseResponse.succeed();
    }
}
