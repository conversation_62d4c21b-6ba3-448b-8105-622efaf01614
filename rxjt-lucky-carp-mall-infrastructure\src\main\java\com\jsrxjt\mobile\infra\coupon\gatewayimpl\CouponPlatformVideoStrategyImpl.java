package com.jsrxjt.mobile.infra.coupon.gatewayimpl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.coupon.rep.common.CouponResult;
import com.coupon.rep.common.PageInfo;
import com.coupon.rep.coupon.AudiovisualListInfo;
import com.coupon.rep.pinnuo.PnCreateOrderResult;
import com.coupon.req.config.CouponConfig;
import com.coupon.req.domain.common.CouponPageQueryBaseRequest;
import com.coupon.req.domain.pinnuo.PnCouponOrderDetailRequest;
import com.coupon.req.domain.recharge.RechargeCreateOrderRequest;
import com.coupon.req.service.CouponService;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.coupon.annotation.CouponTypeHandler;
import com.jsrxjt.mobile.api.coupon.dto.response.CouponCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.coupon.types.CouponTypeEnum;
import com.jsrxjt.mobile.domain.coupon.entity.CouponCardEntity;
import com.jsrxjt.mobile.domain.coupon.entity.CouponGoodsSkuEntity;
import com.jsrxjt.mobile.domain.coupon.entity.CouponPlatformOrderDetail;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformStrategy;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.entity.SubSkuOrderEntity;
import com.jsrxjt.mobile.infra.coupon.utils.CouponPlatformSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 卡管视频直冲卡券
 * <AUTHOR>
 * @date 2025/03/15
 */
@Component
@CouponTypeHandler({CouponTypeEnum.VIDEO})
@RequiredArgsConstructor
@Slf4j
public class CouponPlatformVideoStrategyImpl implements CouponPlatformStrategy {

    private final CouponService couponPlatformService;

    private final CouponConfig couponConfig;

    @Value("${video.order.notifyUrl:}")
    private String notifyUrl;

    @Value("${video.pakcakge.order.notifyUrl:}")
    private String packageNotifyUrl;

    @Override
    public List<CouponGoodsSkuEntity> getAllCouponGoodsSku() {
        CouponPageQueryBaseRequest request = new CouponPageQueryBaseRequest();
        int page = 1;
        request.setPage(page);
        request.setSize(20);
        List<CouponGoodsSkuEntity> allCouponGoodsSkuEntity = new ArrayList<>();
        List<AudiovisualListInfo> couponInfoList = new ArrayList<>();
        log.info("开始获取卡管视频直充卡券");
        do {
            //间隔1000毫秒 不然会提示限流
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            CouponResult couponResult = couponPlatformService.getAudiovisualMemberListPage(request);
            if (couponResult != null && couponResult.getCode() == 0 && couponResult.getData() != null) {
                PageInfo<AudiovisualListInfo> data = (PageInfo<AudiovisualListInfo>) couponResult.getData();
                couponInfoList = data.getList();
                if (CollectionUtil.isNotEmpty(couponInfoList)) {
                    for (AudiovisualListInfo couponInfo : couponInfoList) {
                        allCouponGoodsSkuEntity.add(fillCouponGoodsSkuEntity(couponInfo));
                    }
                    page++;
                    request.setPage(page);
                    log.info("成功获取第{}页卡管视频直充卡券，本页获取数量：{}", page - 1, couponInfoList.size());
                } else {
                    log.info("第{}页没有获取到卡管视频直充卡券", page);
                    break;
                }
            } else {
                log.error("获取卡管视频直充卡券失败，返回结果：{}", couponResult);
                throw new BizException("获取卡管视频直充卡券失败");
            }
        } while (CollectionUtil.isNotEmpty(couponInfoList));

        log.info("结束获取卡管视频直充卡券，总获取数量：{}", allCouponGoodsSkuEntity.size());
        return allCouponGoodsSkuEntity;
    }

    @Override
    public CouponCreateOrderResponseDTO pushCouponOrder(OrderInfoEntity orderInfo) {
        OrderItemEntity orderItem = orderInfo.getOrderItems().get(0);
        RechargeCreateOrderRequest request = new RechargeCreateOrderRequest();
        request.setGoodsId(Long.valueOf(orderItem.getOutGoodsId()));
        request.setAccount(orderInfo.getRechargeAccount() == null ? orderInfo.getCustomerMobile() : orderInfo.getRechargeAccount());
        request.setThirdOrderSn(orderInfo.getOrderNo());
        request.setNotifyUrl(notifyUrl);
        CouponResult<PnCreateOrderResult> couponResult = couponPlatformService.audiovisualMemberRecharge(request);
        CouponCreateOrderResponseDTO response = new CouponCreateOrderResponseDTO();
        if (couponResult != null && couponResult.getCode() == 0 && couponResult.getData() != null) {
            log.info("创建卡管视听会员直充订单成功：返回卡管订单号 {}", couponResult.getData());
            response.setExternalOrderNo(couponResult.getData().getOrderSn());
        } else if (couponResult != null && couponResult.getCode() != null && couponResult.getCode() != 0) {
            log.error("创建卡管视频直充订单失败，返回结果：{}", couponResult);
            response.setCode(couponResult.getCode());
            response.setMsg(couponResult.getMsg()== null ? "" : couponResult.getMsg());
        } else {
            log.error("创建卡管视听会员直充订单异常，返回结果：{}", JSON.toJSONString(couponResult));
            throw new BizException("创建卡管视听会员直充订单失败");
        }
        return response;
    }

    @Override
    public CouponCreateOrderResponseDTO pushSubCouponOrder(SubSkuOrderEntity subSkuOrder) {

        RechargeCreateOrderRequest request = new RechargeCreateOrderRequest();
        request.setGoodsId(Long.valueOf(subSkuOrder.getOuterId()));
        request.setAccount(subSkuOrder.getRechargeAccount());
        request.setThirdOrderSn(subSkuOrder.getSubOrderNo());
        request.setNotifyUrl(packageNotifyUrl);
        CouponResult<PnCreateOrderResult> couponResult = couponPlatformService.audiovisualMemberRecharge(request);
        CouponCreateOrderResponseDTO response = new CouponCreateOrderResponseDTO();
        if (couponResult != null && couponResult.getCode() == 0 && couponResult.getData() != null) {
            log.info("创建套餐子产品卡管视听会员直充订单成功：返回卡管订单号 {}", couponResult.getData());
            response.setExternalOrderNo(couponResult.getData().getOrderSn());
        } else {
            log.error("创建套餐子产品卡管视听会员直充订单失败，返回结果：{}", JSON.toJSONString(couponResult));
            throw new BizException("创建套餐子产品卡管视听会员直充订单失败");
        }
        return response;
    }

    @Override
    public List<CouponCardEntity> getCouponCardInfo(String externalOrderNo) {
        throw new UnsupportedOperationException("试听直充类型的卡券不支持获取具体卡包信息");
    }

    @Override
    public CouponPlatformOrderDetail getCouponPlatformOrderDetail(String orderNo) {
        PnCouponOrderDetailRequest request = new PnCouponOrderDetailRequest();
        request.setThirdOrderSn(orderNo);
        couponPlatformService.getAudiovisualOrderDetail(request);
        return null;
    }

    @Override
    public String getParametersSign(Map<String, Object> params) {
        return CouponPlatformSignUtil.signStr(params,couponConfig.getAppSecret());
    }

    @Override
    public Integer getInventory(String couponId) {
        throw new UnsupportedOperationException("试听直充类型的卡券不支持获取库存");
    }

    private CouponGoodsSkuEntity fillCouponGoodsSkuEntity(AudiovisualListInfo couponInfo){
        CouponGoodsSkuEntity couponGoodsSkuEntity = new CouponGoodsSkuEntity();
        couponGoodsSkuEntity.setCouponPlatformId(String.valueOf(couponInfo.getGoodsId()));
        if (couponInfo.getAmount() != null){
            couponGoodsSkuEntity.setAmount(new BigDecimal(couponInfo.getAmount()).setScale(2,BigDecimal.ROUND_HALF_UP));
        }
        if (couponInfo.getSettlePrice() != null){
            couponGoodsSkuEntity.setCostPrice(new BigDecimal(couponInfo.getSettlePrice()).setScale(2,BigDecimal.ROUND_HALF_UP));
        }
        if (couponInfo.getPrice() != null){
            couponGoodsSkuEntity.setPrice(new BigDecimal(couponInfo.getPrice()).setScale(2,BigDecimal.ROUND_HALF_UP));
        }
        if (couponInfo.getStatus() != null){
            couponGoodsSkuEntity.setCenterStatus(couponInfo.getStatus());
        }
        if (couponInfo.getBuyLimit() != null){
            couponGoodsSkuEntity.setRationSaleNum(couponInfo.getBuyLimit());
        }
        Integer accountType = couponInfo.getAccountType();
        if (accountType != null){
            couponGoodsSkuEntity.setAccountType(accountType);
        }
        //品诺直冲类型
        Integer type = couponInfo.getType();
        if (type != null){
            couponGoodsSkuEntity.setPnType(type);
        }
        return couponGoodsSkuEntity;
    }
}
