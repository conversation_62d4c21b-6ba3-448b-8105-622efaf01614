package com.jsrxjt.mobile.infra.app.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.domain.app.entity.AlipayTabCategoryEntity;
import com.jsrxjt.mobile.domain.app.repository.AlipayTabCategoryRepository;
import com.jsrxjt.mobile.infra.app.persistent.mapper.AlipayTabCategoryMapper;
import com.jsrxjt.mobile.infra.app.persistent.po.AlipayTabCategoryPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class AlipayTabCategoryRepositoryImpl implements AlipayTabCategoryRepository {
    private final AlipayTabCategoryMapper alipayTabCategoryMapper;

    /**
     * 获取已使用红包分类
     *
     * @return
     */
    @Override
    @Cacheable(cacheNames = "app:tab:category",key = "'all'", unless = "#result == null or #result.isEmpty()")
    public List<AlipayTabCategoryEntity> getUsedTabCategory() {
        LambdaQueryWrapper<AlipayTabCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlipayTabCategoryPO::getStatus, 1);
        queryWrapper.orderByDesc(AlipayTabCategoryPO::getSort);
        List<AlipayTabCategoryPO> poList = alipayTabCategoryMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(poList, AlipayTabCategoryEntity.class);
    }

    /**
     * 获取红包分类详情
     *
     * @param id
     * @return
     */
    @Override
    @Cacheable(cacheNames = "app:tab:category:info", key = "#id",unless = "#result == null")
    public AlipayTabCategoryEntity getTabCategoryDetail(Long id) {
      LambdaQueryWrapper<AlipayTabCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
      queryWrapper.eq(AlipayTabCategoryPO::getId, id);
      queryWrapper.eq(AlipayTabCategoryPO::getStatus, 1);
      AlipayTabCategoryPO po = alipayTabCategoryMapper.selectOne(queryWrapper);
      if(po==null){
          log.warn("未找到分类ID为{}的红包分类", id);
          return null;
      }
      return BeanUtil.toBean(po, AlipayTabCategoryEntity.class);
    }
}
