package com.jsrxjt.adapter.customer.controller;

import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.util.CommonUtils;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.customer.request.*;
import com.jsrxjt.mobile.api.customer.response.*;
import com.jsrxjt.mobile.api.distribution.dto.request.InformationAggregationRequestDto;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.biz.oss.UploadImageService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员管理
 */
@RestController
@RequestMapping("/v1/customer")
@Slf4j
@RequiredArgsConstructor
public class CustomerController {

    private final CustomerService customerService;

    private final UploadImageService uploadImageService;

    @PostMapping("/login")
    @Operation(summary = "登录")
    public BaseResponse<CustomerResponse> login(@RequestBody @Valid CustomerLoginRequest request){
        return BaseResponse.succeed(customerService.login(request));
    }

    @PostMapping("/sendVerificationCode")
    @Operation(summary = "获取验证码")
    public BaseResponse sendVerificationCode(@RequestBody @Valid CustomerSendVerificationCodeRequest request){
        customerService.sendVerificationCode(request);
        return BaseResponse.succeed();
    }

    @PostMapping("/bindPhone")
    @Operation(summary = "新注册用户-绑定手机号码")
    public BaseResponse<CustomerResponse> bindPhone(@RequestBody @Valid CustomerBindPhoneRequest request){
        return BaseResponse.succeed(customerService.bindPhone(request));
    }

    @VerifySign(hasToken=true)
    @PostMapping("/getCustomerInfo")
    @Operation(summary = "获取用户信息")
    public BaseResponse<CustomerInfoResponse> getCustomerInfo(@RequestBody @Valid CustomerRequest request){
        return BaseResponse.succeed(customerService.getCustomerInfo(request.getCustomerId()));
    }

    @VerifySign(hasToken=true)
    @PostMapping("/getPersonalCenterAd")
    @Operation(summary = "获取个人中心广告列表")
    public BaseResponse<List<AdvertisementInfoDTO>> getPersonalCenterAd(@RequestBody @Valid InformationAggregationRequestDto requestDto){
        return BaseResponse.succeed(customerService.getPersonalCenterAd(requestDto.getRegionId()));
    }

    @VerifySign(hasToken=true)
    @PostMapping("/editCustomerInfo")
    @Operation(summary = "编辑用户信息")
    public BaseResponse editCustomerInfo(@RequestBody @Valid CustomerEditRequest request){
        customerService.editCustomerInfo(request);
        return BaseResponse.succeed();
    }

    @VerifySign(hasToken=true)
    @PostMapping("/addOrModifyPassword")
    @Operation(summary = "新增或修改用户密码")
    public BaseResponse addOrModifyPassword(@RequestBody @Valid CustomerPasswordEditRequest request){
        customerService.addOrModifyPassword(request);
        return BaseResponse.succeed();
    }

    @VerifySign(hasToken=true)
    @PostMapping("/setFreePassword")
    @Operation(summary = "开启或关闭小额免密")
    public BaseResponse setFreePassword(@RequestBody @Valid CustomerSetFreePasswordRequest request){
        customerService.setFreePassword(request);
        return BaseResponse.succeed();
    }

    @VerifySign(hasToken = true)
    @PostMapping("/getCardList")
    @Operation(summary = "获取用户卡列表")
    public BaseResponse<List<CustomerCardResponse>> getCardList(@RequestBody @Valid CustomerCardRequest request){
        return BaseResponse.succeed(customerService.getCardList(request));
    }

    @GetMapping("/getLogOffConfig")
    @Operation(summary = "获取注销相关配置")
    public BaseResponse<LoginOffConfigResponse> checkIsAllowedLoginOff(){
        return BaseResponse.succeed(customerService.getLoginOffConfig());
    }

//    @PostMapping("/checkIsAllowedLoginOff")
//    @Operation(summary = "注销前校验")
//    public BaseResponse<CustomerCheckLogOffResponse> checkIsAllowedLoginOff(@RequestBody @Valid CustomerRequest request){
//        return BaseResponse.succeed(customerService.checkIsAllowedLoginOff(request));
//    }

    @PostMapping("/deleteAccount")
    @Operation(summary = "账户注销")
    public BaseResponse deleteAccount(@RequestBody @Valid CustomerDeleteAccountRequest request){
        customerService.deleteAccount(request);
        return BaseResponse.succeed();
    }

    @PostMapping("/bindCard")
    @Operation(summary = "绑定卡")
    public BaseResponse bindCard(@RequestBody @Valid CustomerBindCardRequest request){
        customerService.bindCard(request);
        return BaseResponse.succeed();
    }

    @PostMapping("/unbindCard")
    @Operation(summary = "解绑卡")
    public BaseResponse unbindCard(@RequestBody @Valid CustomerUnbindCardRequest request){
        customerService.unbindCard(request);
        return BaseResponse.succeed();
    }

    @VerifySign(hasToken = true)
    @PostMapping("/setPaySort")
    @Operation(summary = "设置用户卡支付顺序")
    public BaseResponse setPaySort(@RequestBody @Valid CustomerSetPaySortRequest request){
        customerService.setPaySort(request);
        return BaseResponse.succeed();
    }

    @VerifySign(hasToken = true)
    @PostMapping("/getPaySort")
    @Operation(summary = "获取用户卡支付顺序")
    public BaseResponse<CustomerPaySortResponse> getPaySort(@RequestBody @Valid CustomerRequest request){
        return BaseResponse.succeed(customerService.getPaySort(request.getCustomerId()));
    }

    @PostMapping("/upload/image")
    @Operation(summary = "上传图片")
    public BaseResponse<Object> uploadImage(@RequestBody @Valid AliyunOssRequest request){
        String fileExtension = request.getFileType().startsWith(".") ? request.getFileType() : "." + request.getFileType();
        if (!CommonUtils.isValidImageExtension(fileExtension)) {
            return BaseResponse.fail("只允许上传图片文件(jpg/jpeg/png/gif)");
        }
        String newFileName = CommonUtils.generateImageName() + fileExtension;
        return uploadImageService.uploadImage(newFileName, request.getData());
    }

    @VerifySign(hasToken=true)
    @PostMapping("/checkCustomerHasPayPassword")
    @Operation(summary = "校验用户是否设置过支付密码")
    public BaseResponse<Boolean> checkCustomerHasPayPassword(@RequestBody @Valid CustomerRequest request){
        return BaseResponse.succeed(customerService.checkCustomerHasPayPassword(request.getCustomerId()));
    }

    @VerifySign(hasToken=true)
    @PostMapping("/getBalanceByCardNoAndPassword")
    @Operation(summary = "根据卡号和卡密查询卡余额")
    public BaseResponse<CardBalanceResponse> getBalanceByCardNoAndPassword(@RequestBody @Valid CardBalanceQueryRequest request){
        return BaseResponse.succeed(customerService.getBalanceByCardNoAndPassword(request));
    }
}
