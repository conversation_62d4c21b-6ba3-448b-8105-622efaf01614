package com.jsrxjt.mobile.api.product.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;


@Data
public class RechargeUsageInstructionsRequestDTO extends BaseParam {

    @Schema(description = "102-视听会员充值 402-支付宝红包")
    @NotNull(message = "产品类型不能为空")
    private Integer productType;

    @Schema(description = "产品的spuId")
    @NotNull(message = "spuId不能为空")
    private Long productSpuId;
}
