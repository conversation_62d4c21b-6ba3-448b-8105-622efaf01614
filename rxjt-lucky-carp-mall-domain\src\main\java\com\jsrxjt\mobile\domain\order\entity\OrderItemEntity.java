package com.jsrxjt.mobile.domain.order.entity;


import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单明细表实体类
 */
@Data

public class OrderItemEntity {

    /**
     * 订单项ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 用户ID
     */
    private Long customerId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品类型: 1-卡券 2-套餐 3-普通应用 4-红包/充值应用
     */
    private Integer productType;

    /**
     * 扁平化商品类型: 商品类型 *100 + 子类型
     */
    private Integer flatProductType;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品SKU ID
     */
    private Long productSkuId;

    /**
     * 商品SKU名称
     */
    private String productSkuName;

    /**
     * 商品一级分类ID
     */
    private Long firstCategoryId;

    /**
     * 商品分类ID
     */
    private Long categoryId;

    /**
     * 商品logo
     */
    private String productLogo;

    /**
     * 商品额外信息 (JSON格式)
     */
    private String extraInfo;

    /**
     * 商品规格名称
     */
    private String productSpecsName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 销售单价
     */
    private BigDecimal sellPrice;

    /**
     * 实际销售单价
     */
    private BigDecimal actualPrice;

    /**
     * 促销活动id
     */
    private Long promotionActivityId;

    /**
     * 成本单价
     */
    private BigDecimal costPrice;

    /**
     * 数量，默认值为1
     */
    private Integer quantity = 1;

    /**
     * 单个商品加点手续费，默认值为0.00
     */
    private BigDecimal serviceFee = BigDecimal.ZERO;

    /**
     * 单个商品加点手续费率(%)，默认值为0.00
     */
    private BigDecimal serviceFeeRate = BigDecimal.ZERO;

    /**
     * SKU面值
     */
    private BigDecimal faceAmount;

    /**
     * 商品来源表
     */
    private String sourceTable;

    /**
     * 外部商品id(productType=1时对应卡管的coupon_id)
     */
    private String outGoodsId;

    /**
     * 外部商品类型
     */
    private String outGoodsType;

    /**
     * 应用型产品的appFlag
     */
    private String appFlag;

    /**
     * 创建时间，默认当前时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间，默认当前时间，自动更新
     */
    private LocalDateTime modTime;

    /**
     * 子SKU订单列表（套餐商品拆分后的子订单）
     */
    private List<SubSkuOrderEntity> subSkuOrders;
}
