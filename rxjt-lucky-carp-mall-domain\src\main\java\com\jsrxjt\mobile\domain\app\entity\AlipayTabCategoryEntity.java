package com.jsrxjt.mobile.domain.app.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(name = "AlipayTabCategory", description = "支付宝tab分类表")
public class AlipayTabCategoryEntity {


    @Schema(description = "id")
    private Long id;

    @Schema(description = "tab分类名称")
    private String categoryName;

    @Schema(description = "排序（降序）")
    private Integer sort;

    @Schema(description = "状态 0关闭 1启动")
    private Integer status;

    @Schema(description = "类目属性 1银行 2其他")
    private Integer attribute;
}
