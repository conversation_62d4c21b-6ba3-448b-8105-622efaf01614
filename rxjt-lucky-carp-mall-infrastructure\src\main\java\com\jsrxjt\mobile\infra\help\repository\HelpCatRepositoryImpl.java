package com.jsrxjt.mobile.infra.help.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.mobile.domain.help.entity.HelpCatEntity;
import com.jsrxjt.mobile.domain.help.repository.HelpCatRepository;
import com.jsrxjt.mobile.infra.help.mapper.HelpCatMapper;
import com.jsrxjt.mobile.infra.help.po.HelpCatPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class HelpCatRepositoryImpl implements HelpCatRepository {
    private final HelpCatMapper helpCatMapper;
    private final RedisUtil redisUtil;

    /**
     * 根据状态查询所有分类
     *
     * @return
     */
    @Override
//    @Cacheable(value = "help:cat:list",unless = "#result == null or #result.isEmpty()")
    public List<HelpCatEntity> findAllByStatus() {
        List<HelpCatPO> helpCatPOS = null;
        String catStr = redisUtil.get(RedisKeyConstants.HELP_CAT_LIST);
        if (StringUtils.isNotEmpty(catStr)) {
            helpCatPOS = JSON.parseArray(catStr, HelpCatPO.class);
        } else {
            LambdaQueryWrapper<HelpCatPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HelpCatPO::getStatus, 1);
            queryWrapper.eq(HelpCatPO::getDelFlag, 0);
            queryWrapper.orderByDesc(HelpCatPO::getSort);
            helpCatPOS = helpCatMapper.selectList(queryWrapper);
            if (CollectionUtil.isNotEmpty(helpCatPOS)) {
                redisUtil.set(RedisKeyConstants.HELP_CAT_LIST, JSON.toJSONString(helpCatPOS), true);
            }

        }
        return BeanUtil.copyToList(helpCatPOS, HelpCatEntity.class);
    }
}
