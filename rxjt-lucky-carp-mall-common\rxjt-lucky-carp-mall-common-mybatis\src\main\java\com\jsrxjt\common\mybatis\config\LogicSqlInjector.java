package com.jsrxjt.common.mybatis.config;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.jsrxjt.common.mybatis.methods.InsertAllBatch;
import com.jsrxjt.common.mybatis.methods.InsertAllColumns;

import java.util.List;

/**
 *  LogicSqlInjector
 * 
 * <AUTHOR> Fengping
 * 2023/3/8 17:59
 * 
 **/
public class LogicSqlInjector extends DefaultSqlInjector {

    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass, TableInfo tableInfo) {
        List<AbstractMethod> methodList = super.getMethodList(mapperClass, tableInfo);
        methodList.add(new InsertAllColumns("insertAllColumns"));
        methodList.add(new InsertAllBatch("insertAllBatch"));
        return methodList;
    }
}
