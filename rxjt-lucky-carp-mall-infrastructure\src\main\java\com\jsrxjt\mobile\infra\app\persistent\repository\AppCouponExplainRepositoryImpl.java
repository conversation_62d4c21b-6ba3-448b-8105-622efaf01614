package com.jsrxjt.mobile.infra.app.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.api.app.types.AppCouponExplainTypeEnum;
import com.jsrxjt.mobile.domain.app.entity.AppCouponExplainEntity;
import com.jsrxjt.mobile.domain.app.repository.AppCouponExplainRepository;
import com.jsrxjt.mobile.infra.app.persistent.mapper.AppCouponExplainMapper;
import com.jsrxjt.mobile.infra.app.persistent.po.AppCouponExplainPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@RequiredArgsConstructor
@Slf4j
public class AppCouponExplainRepositoryImpl implements AppCouponExplainRepository {

    private final AppCouponExplainMapper appCouponExplainMapper;
    /**
     * 根据spuId和类型查询说明
     *
     * @param spuId
     * @param appType
     */
    @Override
    public List<AppCouponExplainEntity> getExplainBySpuIdAndType(Long spuId, Integer appType) {
        LambdaQueryWrapper<AppCouponExplainPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppCouponExplainPO::getAppSpuId, spuId)
                .eq(AppCouponExplainPO::getAppType, appType)
                .eq(AppCouponExplainPO::getDelFlag, 0);
        List<AppCouponExplainPO> appCouponExplainPOList = appCouponExplainMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(appCouponExplainPOList, AppCouponExplainEntity.class);
    }

    @Override
    public AppCouponExplainEntity getBySpuIdAndExplainType(Long spuId, AppCouponExplainTypeEnum explainType) {
        LambdaQueryWrapper<AppCouponExplainPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppCouponExplainPO::getAppSpuId, spuId)
                .eq(AppCouponExplainPO::getExplainType, explainType.getType())
                .eq(AppCouponExplainPO::getDelFlag, 0);
        AppCouponExplainPO appCouponExplainPO = appCouponExplainMapper.selectOne(queryWrapper);
        return BeanUtil.copyProperties(appCouponExplainPO, AppCouponExplainEntity.class);
    }
}
