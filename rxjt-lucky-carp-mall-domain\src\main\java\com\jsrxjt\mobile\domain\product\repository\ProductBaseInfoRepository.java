package com.jsrxjt.mobile.domain.product.repository;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.product.entity.ProductSpuBaseInfo;
import com.jsrxjt.mobile.api.product.dto.response.ProductSuggestionResponseDTO;

import java.util.List;

/**
 * 产品基础信息仓库接口
 * 
 * <AUTHOR>
 * @since 2025/5/8
 **/
public interface ProductBaseInfoRepository {

    /**
     * 分页搜索产品列表
     * 
     * @param keyword 搜索关键词
     * @param page    页码，从1开始
     * @param size    每页大小
     * @return 搜索结果
     */
    PageDTO<ProductSpuBaseInfo> pageProducts(String keyword, Integer page, Integer size);

    /**
     * 搜索产品列表
     *
     * @param keyword 搜索关键词
     * @return 搜索结果
     */
    List<ProductSpuBaseInfo> listProducts(String keyword);

    /**
     * 获取产品联想词
     * 
     * @param keyword  搜索关键词
     * @param maxCount 最大返回数量
     * @return 联想词列表，包含高亮和产品类型
     */
    List<ProductSuggestionResponseDTO> getSuggestions(String keyword, Integer maxCount);

    /**
     * 根据二级分类ID查询产品列表
     *
     * @param categoryId 分类ID
     * @param limit      返回数量限制，默认为10
     * @return 产品列表
     */
    List<ProductSpuBaseInfo> findProductsByCategoryId(String categoryId, Integer limit);

    /**
     * 根据二级分类ID列表查询产品列表
     *
     * @param categoryIds 分类ID列表
     * @param limit       返回数量限制，默认为200
     * @return 产品列表
     */
    List<ProductSpuBaseInfo> findProductsByCategoryIds(List<String> categoryIds, Integer limit);
}
