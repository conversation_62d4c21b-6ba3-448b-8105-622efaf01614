package com.jsrxjt.mobile.infra.app.persistent.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("alipay_tab_category")
@Schema(name = "AlipayTabCategory", description = "支付宝tab分类表")
public class AlipayTabCategoryPO {

    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Long id;

    @Schema(description = "tab分类名称")
    private String categoryName;

    @Schema(description = "排序（降序）")
    private Integer sort;

    @Schema(description = "状态 0关闭 1启动")
    private Integer status;

    @Schema(description = "类目属性 1银行 2其他")
    private Integer attribute;

    @Schema(description = "创建人")
    private Long createId;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改人id")
    private Long modId;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private Date modTime;

    @Schema(description = "删除标记 0、未删除 1、已删除")
    private Integer delFlag;

}
