package com.jsrxjt.mobile.infra.product.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.product.dto.response.ProductSuggestionResponseDTO;
import com.jsrxjt.mobile.domain.product.entity.ProductSpuBaseInfo;
import com.jsrxjt.mobile.domain.product.repository.ProductBaseInfoRepository;
import com.jsrxjt.mobile.infra.product.persistent.doc.ProductInfoDoc;
import com.jsrxjt.mobile.infra.product.persistent.esmapper.ProductInfoEsMapper;
import com.jsrxjt.mobile.infra.product.persistent.mapper.ProductSubscriptMapper;
import com.jsrxjt.mobile.infra.product.persistent.po.ProductSubscriptPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 产品基础信息仓库实现类
 * 
 * <AUTHOR> Fengping
 * @since 2025/5/8
 **/
@Repository
@RequiredArgsConstructor
@Slf4j
public class ProductBaseInfoRepositoryImpl implements ProductBaseInfoRepository {

    private final ProductInfoEsMapper productInfoEsMapper;

    private final ProductSubscriptMapper productSubscriptMapper;

    @Override
    public PageDTO<ProductSpuBaseInfo> pageProducts(String keyword, Integer page, Integer size) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        // 如果关键词为空，返回空结果
        if (StringUtils.isBlank(keyword)) {
            return PageDTO.<ProductSpuBaseInfo>builder()
                    .records(Collections.emptyList())
                    .total(0L)
                    .current(page.longValue())
                    .size(size.longValue())
                    .build();
        }

        try {
            LambdaEsQueryWrapper<ProductInfoDoc> wrapper = getProductInfoDocLambdaEsQueryWrapper(keyword);

            // 执行分页查询
            EsPageInfo<ProductInfoDoc> pageInfo = productInfoEsMapper.pageQuery(wrapper, page, size);

            // 转换为领域实体
            List<ProductSpuBaseInfo> productList = pageInfo.getList().stream()
                    .map(this::convertToEntity)
                    .collect(Collectors.toList());

            PageDTO<ProductSpuBaseInfo> result = PageDTO.<ProductSpuBaseInfo>builder()
                    .records(productList)
                    .total(pageInfo.getTotal())
                    .current((long) pageInfo.getPageNum())
                    .size((long) pageInfo.getPageSize())
                    .build();

            log.info("搜索产品成功，关键词：{}，查询到记录数：{}", keyword, pageInfo.getTotal());
            return result;

        } catch (Exception e) {
            log.error("搜索产品异常，关键词：{}", keyword, e);
            return PageDTO.<ProductSpuBaseInfo>builder()
                    .records(Collections.emptyList())
                    .total(0L)
                    .current(page.longValue())
                    .size(size.longValue())
                    .build();
        }
    }

    @NotNull
    private  LambdaEsQueryWrapper<ProductInfoDoc> getProductInfoDocLambdaEsQueryWrapper(String keyword) {
        LambdaEsQueryWrapper<ProductInfoDoc> wrapper = new LambdaEsQueryWrapper<>();

        wrapper.and(andWrapper -> andWrapper
                .match(ProductInfoDoc::getProductName, keyword, 2.0f)
                .or()
                .match(ProductInfoDoc::getBrandName, keyword, 1.0f)
                .or()
                .match(ProductInfoDoc::getSubTitle, keyword, 0.5f)
        ).eq(ProductInfoDoc::getStatus, 1).limit(200);


        return wrapper;
    }

    @Override
    public List<ProductSpuBaseInfo> listProducts(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return Collections.emptyList();
        }
        LambdaEsQueryWrapper<ProductInfoDoc> esQueryWrapper = getProductInfoDocLambdaEsQueryWrapper(keyword);
        List<ProductInfoDoc> productInfoDocs = productInfoEsMapper.selectList(esQueryWrapper);
        return productInfoDocs.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());

    }

    @Override
    public List<ProductSuggestionResponseDTO> getSuggestions(String keyword, Integer maxCount) {
        if (StringUtils.isBlank(keyword)) {
            return Collections.emptyList();
        }

        try {
            // 创建查询条件
            LambdaEsQueryWrapper<ProductInfoDoc> wrapper = new LambdaEsQueryWrapper<>();
            // 优先匹配产品名称，其次匹配品牌名称,筛选上架条件的产品
            wrapper.and(andWrapper -> andWrapper
                    .match(ProductInfoDoc::getProductName, keyword, 2.0f)
                    .or()
                    .match(ProductInfoDoc::getBrandName, keyword, 1.0f)
                    .or()
                    .match(ProductInfoDoc::getSubTitle, keyword, 0.5f)
            ).eq(ProductInfoDoc::getStatus, 1).limit(maxCount);

            // 执行查询
            List<ProductInfoDoc> docs = productInfoEsMapper.selectList(wrapper);

            // 转换为DTO
            return docs.stream()
                    .map(doc -> {
                        String originalKeyword = doc.getProductName();

                        // 创建带有高亮标签的内容
                        String highlightKeyword = originalKeyword;
                        if (originalKeyword != null && !keyword.isEmpty()) {
                            // 简单地将关键词部分用高亮标签包裹
                            String lowerOriginal = originalKeyword.toLowerCase();
                            String lowerKeyword = keyword.toLowerCase();
                            if (lowerOriginal.contains(lowerKeyword)) {
                                int start = lowerOriginal.indexOf(lowerKeyword);
                                int end = start + lowerKeyword.length();
                                highlightKeyword = originalKeyword.substring(0, start) +
                                        "<span style=\"color:#FF5500;\">" + originalKeyword.substring(start, end) + "</span>" +
                                        originalKeyword.substring(end);
                            }
                        }

                        return ProductSuggestionResponseDTO.builder()
                                .keyword(originalKeyword)
                                .highlightKeyword(highlightKeyword)
                                .productType(doc.getProductType())
                                .build();
                    })
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取联想词异常，关键词：{}", keyword, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ProductSpuBaseInfo> findProductsByCategoryId(String categoryId, Integer limit) {
        if (StringUtils.isBlank(categoryId)) {
            return Collections.emptyList();
        }

        if (limit == null || limit < 1) {
            limit = 20;
        }

        try {
            // 创建查询条件
            LambdaEsQueryWrapper<ProductInfoDoc> wrapper = new LambdaEsQueryWrapper<>();
            wrapper.eq(ProductInfoDoc::getSecondCategoryId, categoryId)
                    .eq(ProductInfoDoc::getShowCategory, true)
                    .eq(ProductInfoDoc::getStatus, 1)
                    .limit(limit);

            // 执行查询
            List<ProductInfoDoc> docs = productInfoEsMapper.selectList(wrapper);

            // 转换为领域实体
            List<ProductSpuBaseInfo> productList = docs.stream()
                    .map(this::convertToEntity)
                    .toList();


            log.info("查询分类产品成功，分类ID：{}，查询到记录数：{}", categoryId, productList.size());
            return productList;

        } catch (Exception e) {
            log.error("查询分类产品异常，分类ID：{}", categoryId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ProductSpuBaseInfo> findProductsByCategoryIds(List<String> categoryIds, Integer limit) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyList();
        }
        if (limit == null || limit < 1) {
            limit = 200;
        }

        try {
            // 创建查询条件
            LambdaEsQueryWrapper<ProductInfoDoc> wrapper = new LambdaEsQueryWrapper<>();
            wrapper.in(ProductInfoDoc::getSecondCategoryId, categoryIds)
                    .eq(ProductInfoDoc::getShowCategory, true)
                    .eq(ProductInfoDoc::getStatus, 1)
                    .limit(limit);

            // 执行查询
            List<ProductInfoDoc> docs = productInfoEsMapper.selectList(wrapper);

            // 转换为领域实体
            List<ProductSpuBaseInfo> productList = docs.stream()
                    .map(this::convertToEntity)
                    .toList();


            log.info("多分类查询产品成功，分类ID列表：{}，查询到记录数：{}", categoryIds, productList.size());
            return productList;

        } catch (Exception e) {
            log.error("多分类查询产品异常，分类ID列表：{}", categoryIds, e);
            return Collections.emptyList();
        }
    }

    /**
     * 将ES文档转换为领域实体
     */
    private ProductSpuBaseInfo convertToEntity(ProductInfoDoc doc) {
        if (doc == null) {
            return null;
        }

        ProductSpuBaseInfo entity = new ProductSpuBaseInfo();
        BeanUtil.copyProperties(doc, entity);
        return entity;
    }

    /**
     * 批量查询角标信息并设置到产品列表中
     * 
     * @param productList 产品列表
     */
    private void batchQueryAndSetSubscriptUrls(List<ProductSpuBaseInfo> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        // 收集所有不为空的角标ID
        List<Long> subscriptIds = productList.stream()
                .map(ProductSpuBaseInfo::getSubscriptId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(subscriptIds)) {
            return;
        }

        try {
            // 批量查询角标信息
            List<ProductSubscriptPO> subscriptList = productSubscriptMapper.selectList(
                    new LambdaQueryWrapper<ProductSubscriptPO>()
                            .in(ProductSubscriptPO::getId, subscriptIds)
                            .eq(ProductSubscriptPO::getStatus, 1));

            // 转换为ID到URL的映射
            Map<Long, String> subscriptIdToUrlMap = subscriptList.stream()
                    .collect(Collectors.toMap(
                            ProductSubscriptPO::getId,
                            ProductSubscriptPO::getSubscriptImg,
                            (existing, replacement) -> existing));

            // 设置角标URL到产品实体中
            productList.forEach(product -> {
                if (product.getSubscriptId() != null) {
                    String subscriptUrl = subscriptIdToUrlMap.get(product.getSubscriptId());
                    product.setSubscriptUrl(subscriptUrl);
                }
            });

            log.info("批量查询角标完成，角标ID数量：{}，查询到角标数量：{}", subscriptIds.size(), subscriptList.size());

        } catch (Exception e) {
            log.error("批量查询角标异常，角标ID列表：{}", subscriptIds, e);
        }
    }
}