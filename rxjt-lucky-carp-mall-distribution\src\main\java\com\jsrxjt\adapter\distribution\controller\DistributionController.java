package com.jsrxjt.adapter.distribution.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.distribution.dto.request.DistributionRedirectBaseDTO;
import com.jsrxjt.mobile.api.locallife.dto.request.LocalLifeRedirectUrlDTO;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.gateway.distribution.UnifiedDistributionApi;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistAccessRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistAccessResponse;
import com.jsrxjt.mobile.domain.locallife.gateway.LocalLifeGateWay;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 分销控制器
 * <AUTHOR> Fengping
 * @since 2025/3/20
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/distribution")
@Tag(name = "分销业务", description = "分销相关接口")
public class DistributionController {

    private final UnifiedDistributionApi unifiedDistributionApi;

    private final LocalLifeGateWay localLifeGateWay;

    private final CustomerService customerService;

    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查分销服务是否正常运行")
    public String health() {
        return "Distribution service is running";
    }


    @PostMapping("/getRedirectUrl")
    @Operation(summary = "获取应用跳转链接")
    @VerifySign(hasToken = true)
    public BaseResponse<String> getRedirectUrl(@RequestBody @Valid DistributionRedirectBaseDTO requestDTO) {
        Long userId = StpUtil.getLoginIdAsLong();
        CustomerEntity customerEntity = customerService.getCustomerById(userId);
        if (customerEntity == null) {
            throw new BizException("用户不存在");
        }
        DistChannelType distChannelType = DistChannelType.getByCode(requestDTO.getDistributionType());
        if (distChannelType == null) {
            return BaseResponse.fail();
        }
        String redirectUrl = null;
        if (distChannelType == DistChannelType.LOCALLIFE) {
            LocalLifeRedirectUrlDTO localLifeRedirectUrlDTO = new LocalLifeRedirectUrlDTO();
            BeanUtils.copyProperties(requestDTO, localLifeRedirectUrlDTO);
            localLifeRedirectUrlDTO.setUserId(userId);
            redirectUrl = localLifeGateWay.getLocalLifeRedirectUrl(localLifeRedirectUrlDTO);
        } else {
            DistAccessRequest request = DistAccessRequest.builder()
                    .channelType(distChannelType)
                    .userId(String.valueOf(userId))
                    .mobile(customerEntity.getPhone())
                    .latitude(requestDTO.getLatitude())
                    .longitude(requestDTO.getLongitude())
                    .build();
            DistAccessResponse distAccessResponse = unifiedDistributionApi.access(request);
            redirectUrl = distAccessResponse.getRedirectUrl();
        }
        return BaseResponse.succeed(redirectUrl);
    }
} 