package com.jsrxjt.adapter.order.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseParam;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPackageDelRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPackageListRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPayDetailPageRequestDTO;
import com.jsrxjt.mobile.api.order.dto.response.CouponPackageDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.CouponPackageListResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.SelCouponPayDetailResponseDTO;
import com.jsrxjt.mobile.api.ticket.TicketDeliveryDetailResponseDTO;
import com.jsrxjt.mobile.api.ticket.TicketDeliveryResponseDTO;
import com.jsrxjt.mobile.api.ticket.TicketShopListResponseDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketDeliveryDetailRequestDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketDeliveryRequestDTO;
import com.jsrxjt.mobile.biz.order.coupon.CouponPackageCaseService;
import com.jsrxjt.mobile.biz.ticket.TicketDeliveryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 卡包接口
 * <AUTHOR>
 * @date 2025/07/22
 */
@RestController
@RequestMapping("/v1/ticketDelivery")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "我的优惠券接口", description = "我的优惠券接口")
public class TicketDeliveryController {

    private final TicketDeliveryService ticketDeliveryService;

    @PostMapping("/getTicketDelivery")
    @Operation(summary = "商家优惠券分页")
    @VerifySign(hasToken = true)
    public BaseResponse<PageDTO<TicketDeliveryResponseDTO>> getTicketDelivery(@RequestBody @Valid TicketDeliveryRequestDTO request){
        return BaseResponse.succeed(ticketDeliveryService.getTicketDelivery(request));
    }

    @PostMapping("/getTicketDeliveryById")
    @Operation(summary = "商家优惠券详情")
    @VerifySign(hasToken = true)
    public BaseResponse<TicketDeliveryDetailResponseDTO> getTicketDeliveryById(@RequestBody @Valid TicketDeliveryDetailRequestDTO request){
        return BaseResponse.succeed(ticketDeliveryService.getTicketDeliveryById(request));
    }

    @PostMapping("/delTicketDelivery")
    @Operation(summary = "删除商家优惠券")
    @VerifySign(hasToken = true)
    public BaseResponse<Void> delTicketDelivery(@RequestBody @Valid TicketDeliveryDetailRequestDTO request){
        ticketDeliveryService.delTicketDelivery(request);
        return BaseResponse.succeed();
    }


    @PostMapping("/getShopTicketList")
    @Operation(summary = "门店优惠券列表")
    @VerifySign(hasToken = false)
    public BaseResponse<PageDTO<TicketShopListResponseDTO>> getShopTicketList(@RequestBody @Valid TicketDeliveryRequestDTO request){
        return BaseResponse.succeed(ticketDeliveryService.getShopTicketList(request));
    }

}
