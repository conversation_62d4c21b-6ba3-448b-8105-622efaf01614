package com.jsrxjt.mobile.api.ticket;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "门店优惠券")
public class TicketShopListResponseDTO {
    @Schema(description = "门店优惠券id")
    private Long id;
    @Schema(description = "面值")
    private String amount;
    @Schema(description = "优惠券卡号")
    private String ticketCode;
    @Schema(description = "优惠券开始时间")
    private String startTime;
    @Schema(description = "优惠券结束时间")
    private String endTime;
    @Schema(description = "优惠券标题")
    private String ticketName;
    @Schema(description = "描述")
    private String remark;

}
