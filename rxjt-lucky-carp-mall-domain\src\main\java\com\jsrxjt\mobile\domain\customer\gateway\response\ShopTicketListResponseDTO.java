package com.jsrxjt.mobile.domain.customer.gateway.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * @Description: 门店优惠券列表响应DTO
 * @Author: 
 * @Date: 2025-09-03
 * @Version: 1.0
 */
@Data
public class ShopTicketListResponseDTO {
    /**
     * 优惠券ID
     */
    private Long id;
    
    /**
     * 开始时间戳
     */
    private Long stime;
    
    /**
     * 结束时间戳
     */
    private Long etime;
    
    /**
     * 优惠券编号
     */
    private String no;
    
    /**
     * 优惠券面值
     */
    private String value;
    
    /**
     * 优惠券标题
     */
    private String title;
    
    /**
     * 优惠券摘要
     */
    private String summary;
    
    /**
     * 开始时间 yyyy-MM-dd
     */
    @JSONField(name = "start_time")
    private String startTime;
    
    /**
     * 结束时间 yyyy-MM-dd
     */
    @JSONField(name = "end_time")
    private String endTime;
}
