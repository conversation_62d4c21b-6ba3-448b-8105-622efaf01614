package com.jsrxjt.mobile.infra.product.persistent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jsrxjt.mobile.infra.product.persistent.po.SearchDetailPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 搜索关键词Mapper接口
 * 
 * <AUTHOR>
 * @since 2025/5/14
 */
@Mapper
public interface SearchDetailMapper extends BaseMapper<SearchDetailPO> {

    /**
     * 查询区域特定的默认搜索词
     * 
     * @param districtId 区域ID
     * @return 搜索词列表
     */
    @Select("SELECT sd.id, sd.keyword, sd.iml_url, sd.jump_type, sd.jump_url, sd.type, sd.status, sd.is_nationwide, sd.sort " +
           "FROM search_detail sd " +
           "JOIN content_region_relation crr ON sd.id = crr.content_id " +
           "WHERE sd.type = #{type} AND sd.status = 1 AND sd.is_nationwide = 0 AND sd.del_flag = 0 " +
           "AND crr.content_type = 4 AND crr.region_id = #{districtId} AND crr.del_flag = 0 " +
           "ORDER BY sd.sort DESC")
    List<SearchDetailPO> selectSpecificKeywords(@Param("districtId") Long districtId, @Param("type") Integer type);
}