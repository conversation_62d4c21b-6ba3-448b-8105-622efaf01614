package com.jsrxjt.mobile.biz.order.coupon.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.vo.BaseParam;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPackageDelRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPackageListRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPayDetailPageRequestDTO;
import com.jsrxjt.mobile.api.order.dto.response.*;
import com.jsrxjt.mobile.api.product.types.FlatProductTypeEnum;
import com.jsrxjt.mobile.api.product.types.ProductExplainTypeEnum;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.biz.coupon.service.CouponCaseService;
import com.jsrxjt.mobile.biz.order.coupon.CouponPackageCaseService;
import com.jsrxjt.mobile.domain.coupon.entity.CouponGoodsEntity;
import com.jsrxjt.mobile.domain.coupon.entity.CouponGoodsSkuEntity;
import com.jsrxjt.mobile.domain.coupon.repository.CouponGoodsRepository;
import com.jsrxjt.mobile.domain.coupon.repository.CouponGoodsSkuRepository;
import com.jsrxjt.mobile.domain.order.entity.*;
import com.jsrxjt.mobile.domain.order.query.CouponPackageListQuery;
import com.jsrxjt.mobile.domain.order.repository.OrderDeliveryRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderItemRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsSkuEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageSkuSubJobEntity;
import com.jsrxjt.mobile.domain.packages.repository.PackageGoodsRepository;
import com.jsrxjt.mobile.domain.packages.repository.PackageGoodsSkuRepository;
import com.jsrxjt.mobile.domain.packages.repository.PackageSubSkuRepository;
import com.jsrxjt.mobile.domain.pickplatform.gateway.PickPlatformCardCodeRequest;
import com.jsrxjt.mobile.domain.pickplatform.gateway.PickPlatformGateway;
import com.jsrxjt.mobile.domain.pickplatform.request.PickCardItemRequest;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayInfoRequest;
import com.jsrxjt.mobile.domain.pickplatform.response.*;
import com.jsrxjt.mobile.domain.product.entity.ProductExplainEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductOffsetPageEntity;
import com.jsrxjt.mobile.domain.product.repository.ProductExplainRepository;
import com.jsrxjt.mobile.domain.product.repository.ProductOffsetPageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.util.IStructureModel;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 卡包服务实现类
 * <AUTHOR>
 * @date 2025/07/22
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CouponPackageCaseServiceImpl implements CouponPackageCaseService {

    private final OrderDeliveryRepository orderDeliveryRepository;
    private final OrderRepository orderRepository;
    private final ProductOffsetPageRepository productOffsetPageRepository;
    private final OrderItemRepository orderItemRepository;
    private final CouponGoodsSkuRepository couponGoodsSkuRepository;
    private final CouponGoodsRepository couponGoodsRepository;
    private final PackageSubSkuRepository packageSubSkuRepository;
    private final PickPlatformGateway pickPlatformGateway;


    @Override
    public PageDTO<CouponPackageListResponseDTO> getCouponPackageList(Long customerId, CouponPackageListRequestDTO request) {

        CouponPackageListQuery query = CouponPackageListQuery.builder()
                .customerId(customerId)
                .queryName(request.getQueryName())
                .pageNum(request.getToPage())
                .pageSize(request.getPageRows())
                .build();

        PageDTO<CouponPackageEntity> couponPackagePage = orderDeliveryRepository.getCustomerCouponPackageList(query);

        PageDTO<CouponPackageListResponseDTO> couponPackageListPage = PageDTO.<CouponPackageListResponseDTO>builder()
                .current(couponPackagePage.getCurrent())
                .size(couponPackagePage.getSize())
                .total(couponPackagePage.getTotal())
                .build();
        if (CollectionUtil.isNotEmpty(couponPackagePage.getRecords())){
            couponPackageListPage.setRecords(couponPackagePage.getRecords().stream().map(this::convertToDTO).collect(Collectors.toList()));
        }
        return couponPackageListPage;
    }

    /**
     * 卡包详情
     *
     * @param couponPackageId
     * @return
     */
    @Override
    public CouponPackageDetailResponseDTO getCouponPackageDetail(Long couponPackageId) {
        CouponPackageDetailResponseDTO dto = new CouponPackageDetailResponseDTO();
        OrderDeliveryEntity orderDelivery = orderDeliveryRepository.findById(couponPackageId);
        if(orderDelivery.getDelFlag()==1){
            throw new BizException("该卡券已删除");
        }
        String orderNo = orderDelivery.getOrderNo();
        OrderInfoEntity byOrderNo = orderRepository.findByOrderNo(orderNo);
        if(byOrderNo == null){
            throw new BizException("订单不存在");
        }
        //获取sku名称
        Long orderItemId = orderDelivery.getOrderItemId();
        OrderItemEntity orderItem = orderItemRepository.findById(orderItemId);
        if(orderItem == null){
            throw new BizException("未找到该订单明细");
        }
        //获取使用说明
        Long productSpuId = byOrderNo.getProductSpuId();
        Integer flatProductType = orderItem.getFlatProductType();
        String useManual = getUseManual(productSpuId,flatProductType);

        dto.setCouponPackageId(orderDelivery.getId());
        dto.setCouponCode(orderDelivery.getCouponCode());
        dto.setCouponName(orderItem.getBrandName() + orderItem.getProductSkuName());
        dto.setCouponPin(orderDelivery.getCouponPin());
        dto.setCouponUrl(orderDelivery.getCouponUrl());
        dto.setCouponUrlPass(orderDelivery.getCouponUrlPass());
        dto.setCouponCrc(orderDelivery.getCouponCrc());
        dto.setLogoUrl(orderItem.getProductLogo());
        dto.setAmountName(orderItem.getFaceAmount().stripTrailingZeros().toPlainString()+"元");
        dto.setAmount(orderItem.getFaceAmount());
        dto.setUseManual(useManual);
        dto.setSpuId(productSpuId);
        dto.setSkuId(orderItem.getProductSkuId());
        //获取核销类型
        SelCouponNameResponseDTO couponInfo1 = getCouponInfo(byOrderNo, orderItem, orderDelivery);
        dto.setFlqType(couponInfo1.getFlgType());
        //自发券动态码-购券明细
        if(couponInfo1.getIsSelfCoupon()==1){
           dto.setCouponName(orderItem.getProductName());  //自发券名称使用spu name
           SelCouponCodeResponseDTO selCouponCodeResponseDTO = convertToDTO(orderDelivery, byOrderNo);
           dto.setSelCouponCodeResponseDTO(selCouponCodeResponseDTO);
        }
        return dto;
    }

    /**
     * 消费明细
     *
     * @param request
     */
    @Override
    public PageDTO<SelCouponPayDetailResponseDTO> consumeList(CouponPayDetailPageRequestDTO request) {
        Long spuId = request.getSpuId();
        Long customerId = StpUtil.getLoginIdAsLong();
       // Long customerId = 378632859374850L;
        CouponGoodsEntity couponInfo = couponGoodsRepository.getCouponInfo(spuId);
        if(couponInfo == null){
            throw new BizException("未找到该卡券");
        }
        Integer pickProductId = couponInfo.getPickProductId();

        PickPlatformPayInfoRequest payInfoRequest = new PickPlatformPayInfoRequest();
        payInfoRequest.setUserNo(String.valueOf(customerId));
        payInfoRequest.setProductId(String.valueOf(pickProductId));
        PickPlatformPayInfoPageResponse cardPayInfo = pickPlatformGateway.getCardPayInfo(payInfoRequest);
        if(cardPayInfo == null){
           return PageDTO.emptyBuild(request.getPageNum().longValue(),request.getPageSize().longValue());
        }
        List<PickPlatformPayInfoResponse> list = cardPayInfo.getList();
        List<SelCouponPayDetailResponseDTO> selCouponPayDetailResponseDTOS = BeanUtil.copyToList(list, SelCouponPayDetailResponseDTO.class);
        return PageDTO.build(selCouponPayDetailResponseDTOS,cardPayInfo.getCount().longValue(),request.getPageSize().longValue(), request.getPageNum().longValue());
    }

    /**
     * 回收站分页
     */
    @Override
    public PageDTO<CouponPackageListResponseDTO> recycleBinList( BaseParam request) {
       Long customerId = StpUtil.getLoginIdAsLong();
        CouponPackageListQuery query = CouponPackageListQuery.builder()
                .customerId(customerId)
                .queryName(null)
                .pageNum(request.getToPage())
                .pageSize(request.getPageRows())
                .build();

        PageDTO<CouponPackageEntity> couponPackagePage = orderDeliveryRepository.getCycCouponPackageList(query);

        PageDTO<CouponPackageListResponseDTO> couponPackageListPage = PageDTO.<CouponPackageListResponseDTO>builder()
                .current(couponPackagePage.getCurrent())
                .size(couponPackagePage.getSize())
                .total(couponPackagePage.getTotal())
                .build();
        if (CollectionUtil.isNotEmpty(couponPackagePage.getRecords())){
            couponPackageListPage.setRecords(couponPackagePage.getRecords().stream().map(this::convertToDTO).collect(Collectors.toList()));
        }
        return couponPackageListPage;
    }

    private SelCouponCodeResponseDTO  convertToDTO(OrderDeliveryEntity entity,OrderInfoEntity orderInfoEntity){
        SelCouponCodeResponseDTO dto = new SelCouponCodeResponseDTO();
        Long customerId = StpUtil.getLoginIdAsLong();
        //Long customerId = 378632859374850L;
        String firstCouponUrl = entity.getCouponUrl();
        String firstCouponOffsetCode = firstCouponUrl.substring(firstCouponUrl.lastIndexOf("=") + 1);

        //核销编号List
        List<String> couponOffsetCodeList = new ArrayList<>();
        Long brandId = orderInfoEntity.getBrandId();
        List<SelOrderDeliveryDetailEntity> selOrderDeliveryDetailList = orderDeliveryRepository.getSelOrderDeliveryDetailList(customerId, brandId);
        if(CollectionUtil.isEmpty(selOrderDeliveryDetailList)){
            log.error("未找到该用户的卡券");
           return dto;
        }
        couponOffsetCodeList = selOrderDeliveryDetailList.stream().map(SelOrderDeliveryDetailEntity::getCheckCode).collect(Collectors.toList());
        //将couponOffsetCodeList中firstCouponOffsetCode放到第一位
        couponOffsetCodeList.remove(firstCouponOffsetCode);
        couponOffsetCodeList.add(0, firstCouponOffsetCode);
        //获取动态码
        PickPlatformCardCodeRequest request = new PickPlatformCardCodeRequest();
        request.setUserNo(String.valueOf(customerId));
        request.setTimestamp(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        request.setNonce(RandomUtil.randomString(16));
        //couponOffsetCodeList转字符串
        request.setCheckNo(couponOffsetCodeList.stream().collect(Collectors.joining(",")));
        PickPlatformCardCodeResponse cardCode = pickPlatformGateway.getCardCode(request);
        if(cardCode == null){
          log.error("获取动态码失败");
          return dto;
        }
        dto.setCode(cardCode.getCode());
        dto.setBalance(cardCode.getBalance());
        dto.setValidTime(cardCode.getValidTime());
        //获取购卡明细
        PickCardItemRequest pickCardItemRequest = new PickCardItemRequest();
        pickCardItemRequest.setCheckNo(couponOffsetCodeList.stream().collect(Collectors.joining(",")));
        pickCardItemRequest.setTimestamp(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        pickCardItemRequest.setNonce(RandomUtil.randomString(16));
        PickCardItemResponse cardInfo = pickPlatformGateway.getCardInfo(pickCardItemRequest);
        if(cardInfo != null && CollectionUtil.isNotEmpty(cardInfo.getList()) ){
            Map<String,SelOrderDeliveryDetailEntity> map = selOrderDeliveryDetailList.stream().collect(Collectors.toMap(SelOrderDeliveryDetailEntity::getCheckCode, v -> v));
            List<SelCouponCodeDetailResponseDTO> detailList = new ArrayList<>();
            List<SelCouponCodeDetailResponseDTO> historyList = new ArrayList<>();
            List<PickCardItemDetailResponse> list = cardInfo.getList();
            for (PickCardItemDetailResponse detail : list) {
                SelOrderDeliveryDetailEntity selOrderDeliveryDetailEntity = map.get(detail.getCheckNo());
                if(selOrderDeliveryDetailEntity != null){
                    if("USABLE".equals(detail.getCardStatus())){
                        SelCouponCodeDetailResponseDTO detailDTO = new SelCouponCodeDetailResponseDTO();
                        detailDTO.setCouponCode(detail.getCardNo());
                        detailDTO.setCouponName(selOrderDeliveryDetailEntity.getProductName());
                        detailDTO.setOrderDate(selOrderDeliveryDetailEntity.getOrderTime());
                        detailDTO.setCouponUrl(selOrderDeliveryDetailEntity.getCouponUrl());
                        detailDTO.setValidTime(detail.getValidTime());
                        detailDTO.setBalance(detail.getBalance());
                        detailDTO.setCardStatus(detail.getCardStatus());
                        detailList.add(detailDTO);
                   }else{
                        SelCouponCodeDetailResponseDTO historyDTO = new SelCouponCodeDetailResponseDTO();
                        historyDTO.setCouponCode(detail.getCardNo());
                        historyDTO.setCouponName(selOrderDeliveryDetailEntity.getProductName());
                        historyDTO.setOrderDate(selOrderDeliveryDetailEntity.getOrderTime());
                        historyDTO.setCouponUrl(selOrderDeliveryDetailEntity.getCouponUrl());
                        historyDTO.setValidTime(detail.getValidTime());
                        historyDTO.setBalance(detail.getBalance());
                        historyDTO.setCardStatus(detail.getCardStatus());
                        historyList.add(historyDTO);
                   }
                }
            }
            dto.setDetailList(detailList);
            dto.setHistoryList(historyList);
        }
        return dto;
    }

    private SelCouponNameResponseDTO getCouponInfo(OrderInfoEntity orderInfoEntity
            ,OrderItemEntity orderItemEntity,OrderDeliveryEntity orderDeliveryEntity){
        SelCouponNameResponseDTO dto = new SelCouponNameResponseDTO();

        Integer flgType = null;
        Integer isSelfConpon = null;
        Integer flatProductType = orderItemEntity.getFlatProductType();
        if(flatProductType==FlatProductTypeEnum.NORMAL_COUPON.getType()){
            CouponGoodsEntity couponInfo = couponGoodsRepository.getCouponInfo(orderInfoEntity.getProductSpuId());
            if(couponInfo != null){
                flgType = couponInfo.getFlqType();
                isSelfConpon = couponInfo.getIsSelfCoupon();
            }
        }else if(flatProductType==FlatProductTypeEnum.PACKAGE.getType()){
            PackageSkuSubJobEntity packageSubSkuById = packageSubSkuRepository.getPackageSubSkuById(orderDeliveryEntity.getMiniSkuId());
            if(packageSubSkuById != null){
                flgType = packageSubSkuById.getFlqType();
            }
        }
        SelCouponNameResponseDTO nameDto = new SelCouponNameResponseDTO();
        nameDto.setFlgType(flgType);
        nameDto.setIsSelfCoupon(isSelfConpon);
        return nameDto;
    }

    private String getUseManual(Long productSpuId,Integer flatProductType){
        String useManual = "";
        Byte productType = null;
        if(flatProductType == FlatProductTypeEnum.NORMAL_COUPON.getType()){
            productType = ProductTypeEnum.COUPON.getType().byteValue();
        }else if(flatProductType == FlatProductTypeEnum.PINO_COUPON.getType()){
            productType = ProductTypeEnum.PACKAGE.getType().byteValue();
        }
        if(productType == null){
            throw new BizException("未知产品类型");
        }
        List<ProductOffsetPageEntity> offsetList = productOffsetPageRepository.findBySpuId(productSpuId, productType);
        if(CollectionUtil.isNotEmpty(offsetList)){
            ProductOffsetPageEntity productOffsetPageEntity = offsetList.get(0);
            useManual = productOffsetPageEntity.getRemark();
        }
        return useManual;
    }

    /**
     * 放入回收站
     *
     * @param request
     */
    @Override
    public void putInRecycleBin(CouponPackageDelRequestDTO request) {
        List<Long> couponPackageIdList = request.getCouponPackageIdList();
        //回收站
        orderDeliveryRepository.updateOrderDelivery(couponPackageIdList,0,2);
    }

    /**
     * 回收站恢复
     *
     * @param request
     */
    @Override
    public void restoreRecycleBin(CouponPackageDelRequestDTO request) {
        List<Long> couponPackageIdList = request.getCouponPackageIdList();
        //回收站
        orderDeliveryRepository.updateOrderDelivery(couponPackageIdList,2,0);
    }

    /**
     * 删除卡包
     *
     * @param request
     */
    @Override
    public void deleteCouponPackage(CouponPackageDelRequestDTO request) {
        List<Long> couponPackageIdList = request.getCouponPackageIdList();
        //回收站
        orderDeliveryRepository.updateOrderDelivery(couponPackageIdList,2,1);
    }

    private CouponPackageListResponseDTO convertToDTO(CouponPackageEntity entity) {
        return  CouponPackageListResponseDTO.builder()
                .brandName(entity.getBrandName())
                .couponNum(entity.getCouponNum())
                .brandLogo(entity.getBrandLogo())
                .couponList(entity.getCouponList().stream().map(couponPackageListEntity ->{
                            CouponPackageResponseDTO couponPackageResponseDTO = new CouponPackageResponseDTO();
                            BeanUtil.copyProperties(couponPackageListEntity, couponPackageResponseDTO);
                            return couponPackageResponseDTO;}
                        ).collect(Collectors.toList()))
                .build();
    }
}
