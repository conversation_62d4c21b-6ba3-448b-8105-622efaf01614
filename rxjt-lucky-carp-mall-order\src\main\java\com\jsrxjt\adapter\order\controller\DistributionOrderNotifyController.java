package com.jsrxjt.adapter.order.controller;

import com.jsrxjt.mobile.api.distribution.dto.request.DistributionBaseCreateOrderDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.DistributionCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.biz.distribution.service.DistributionOrderService;
import com.jsrxjt.mobile.domain.order.service.strategy.impl.CommonDistributionOrderInfoBuilder;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;

/**
 * <AUTHOR>
 * @Date 2025/7/17 16:38
 * 分销应用下单通知接口
 */
@RestController
@RequestMapping("/v1/distribution-order-notify")
@RequiredArgsConstructor
public class DistributionOrderNotifyController {

    private final DistributionOrderService distributionOrderService;

    private final CommonDistributionOrderInfoBuilder commonDistributionOrderInfoBuilder;

    /**
     * 通用分销应用创建订单
     *
     * @param request
     * @return
     */
    @PostMapping("/common/created")
    public DistributionCreateOrderResponseDTO commonDistributionOrderCreatedNotify(@RequestBody @Valid DistributionBaseCreateOrderDTO request) {
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();
        createOrderDTO.setCustomerId(Long.valueOf(request.getUserId()));
        createOrderDTO.setExternalOrderNo(request.getOrderNo());
        createOrderDTO.setTradeNo(request.getTradeNo());
        // 兼容金额字段不同场景
        BigDecimal externalOrderAmount = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(request.getTotalAmount())) {
            externalOrderAmount = BigDecimal.valueOf(Double.valueOf(request.getTotalAmount()));
        }
        if (StringUtils.isNotBlank(request.getTradeAmount())) {
            externalOrderAmount = BigDecimal.valueOf(Double.valueOf(request.getTradeAmount()));
        }
        createOrderDTO.setExternalAppProductPrice(externalOrderAmount);
        // 兼容不同场景订单支付超时时间
        String orderTime = request.getOrderTime();
        String orderExpire = request.getOrderExpire();
        if (StringUtils.isNotBlank(orderTime) && StringUtils.isNotBlank(orderExpire)) {
            Instant originalInstant = Instant.ofEpochSecond(Long.valueOf(orderTime));
            // 增加分钟
            Instant newInstant = originalInstant.plus(Duration.ofMinutes(Long.valueOf(orderExpire)));
            // 获取新的时间戳
            long externalOrderExpireTime = newInstant.getEpochSecond();
            createOrderDTO.setExternalOrderExpireTime(String.valueOf(externalOrderExpireTime));

        }
        if (StringUtils.isNotBlank(request.getExpireTime())) {
            createOrderDTO.setExternalOrderExpireTime(request.getExpireTime());
        }
        // 兼容订单支付完成跳转页面
        String externalPayResultUrl = request.getResultPageUrl();
        if (StringUtils.isNotBlank(externalPayResultUrl)) {
            externalPayResultUrl = request.getResultUrl();
        }
        if (StringUtils.isNotBlank(externalPayResultUrl)) {
            externalPayResultUrl = request.getDetailUrl();
        }
        if (StringUtils.isNotBlank(externalPayResultUrl)) {
            externalPayResultUrl = request.getReturnUrl();
        }
        createOrderDTO.setExternalPayResultUrl(externalPayResultUrl);

        DistributionCreateOrderResponseDTO responseDTO = distributionOrderService.distributionCreateOrder(createOrderDTO, commonDistributionOrderInfoBuilder);
        responseDTO.setCashierUrl("");
        return responseDTO;
    }
}
