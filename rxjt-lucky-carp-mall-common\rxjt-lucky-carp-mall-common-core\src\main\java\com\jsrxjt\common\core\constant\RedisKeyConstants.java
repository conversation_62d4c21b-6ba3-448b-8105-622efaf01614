package com.jsrxjt.common.core.constant;

/**
 * The Class RedisKeyConstants.
 */
public class RedisKeyConstants {

    // 用户信息缓存 a001_手机号码 全量
    public static final String SIGN_NONCE = "sign:nonce:";

    /**
     * ================卡券/套餐模块 begin===================
     */
    //直充 coupon:allvideo:spu:info
    public static final String COUPON_ALL_VIDEO_SPU_INFO = "coupon:allvideo:spu:info";

    // 卡券spu信息 CouponGoodsDetailResponse
    public static final String COUPON_SPU_INFO = "coupon:spu:info:";
    // 卡券sku信息 CouponGoodsSkuAllDto
    public static final String COUPON_SKU_INFO = "coupon:sku:info:";

    // 套餐spu信息 PackageGoodsDetailResponse
    public static final String PACKAGE_SPU_INFO = "package:spu:info:";
    // 套餐sku信息
    public static final String PACKAGE_SKU_INFO = "package:sku:info:";
    //套餐子sku信息
    public static final String PACKAGE_SUB_SKU_INFO = "package:sub:sku:info:";

    /**
     * ================卡券/套餐模块 end===================
     */

    /**
     * ================区域 begin===================
     */
    public static final String REGION_CITY = "region:city";

    public static final String REGION_HOT_CITY = "region:hotcity";

    public static final String REGION_ADCODE = "region:adcode";

    public static final String REGION_LOCATION_CUSTOMER = "region:location:customer:";
    /**
     * ================区域 end===================
     */

    /**
     * ================首页扫码付模块 begin===================
     */

    // 付款码时间缓存 off:scan:user:type:%coustomerId:%type
    public static final String OFF_SCAN_USER_APP = "off:scan:user:app:%s:%s";
    // 付款码用户id缓存
    public static final String OFF_SCAN_CODE = "off:scan:code:%s";
    // 预下单缓存 %coustomerId:%外部订单号
    public static final String OFF_PRE_ORDER = "off:pre:order:%s:%s";

    //支付结果缓存 外部订单id
    public static final String CASHIER_PAY_RESULT = "cashier_pay:result:%s";
    //扫码付应用列表
    public static final String ALL_SCAN_APP = "app:goods:types:4-6";
    /**
     * ================首页扫码付模块 end===================
     */

    /**
     * ================展码付模块 begin===================
     */
    // 付码缓存-(userId:code = 应用信息 )
    public static final String PICK_SCAN_CODE = "pick:scan:code:%s";

    // 缓存订单信息 （outOrderCode = 外部订单信息）
    public static final String PICK_SCAN_ORDER = "pick:scan:order:%s";
    /**
     * ================展码付模块 end===================
     */

    /**
     * ================装修页 begin===================
     */
    public static final String CURRENT_HOME_PAGE = "currentHomePage";

    // 页面区域缓存 page:region:{activityId}:{pageId}:{cityId}:{districtId} 首页pageId=0 存储pageRegionId
    public static final String PAGE_REGION = "page:region:%d:%d:%d:%d";

    // 页面基础组件列表 page:info:{pageRegionId}
    public static final String PAGE_INFO = "page:info:";

    public static final String PAGE_QUEUE_KEY = "page:publish:queue";

    // tab栏全球购商品列表
    public static final String PAGE_TAB_GLOBAL_GOODS = "page:tab:global:goods";

    // tab栏本地生活商品列表 hash结构   field结构"{siteId}:{type}"  siteId站点id  type 10:推荐 50:全部
    public static final String PAGE_TAB_LOCAL_LIFE = "page:tab:local:life";

    // 本地生活站点信息  "page:tab:local:life:station:{siteAreaName}"  存储站点基本信息、站点商品数量用于筛选本地生活tab是否显示
    public static final String PAGE_TAB_LOCAL_LIFE_STATION = "page:tab:local:life:station:";

    // tab栏商品列表 "page:tab:info:{pageRegionId}:{moduleDetailId}"
    public static final String PAGE_TAB_INFO = "page:tab:info:%d:%d";

    /**
     * ================装修页 end===================
     */

    /**
     *
     * ================帮助中心模块 begin===================
     */

    public static final String HELP_DETAIL_INFO = "help:detail:info:%s";
    public static final String HELP_CAT_INFO = "help:cat:info";

    /**
     * ================帮助中心模块 end===================
     */

    /**
     * ================卡券营销活动 begin===================
     */
    // 活动基本信息缓存 promotion:activity:{activityId} 活动基本信息随时可编辑，单独存缓存
    public static final String PROMOTION_ACTIVITY_INFO = "promotion:activity:";

    // sku活动信息缓存 promotion:sku:{activityId}:{skuId}:{couponType}
    public static final String PROMOTION_SKU_INFO = "promotion:sku:%d:%d:%d";

    // sku参加的所有活动id缓存 Sorted Set promotion:activity:sku:{skuId}:{couponType}
    // value:activityId
    // score:endTimestamp
    public static final String PROMOTION_ACTIVITY_SKU_RELATION = "promotion:activity:sku:%d:%d";
    /**
     * ================卡券营销活动 end===================
     */

    /**
     * ================全站通用配置 begin===================
     */
    public static final String ORDER_AUTO_CANCEL_TIME = "system:order:auto:cancel:time";
    public static final String MOD_PHONE_MAX_COUNT = "system:mod:phone:max:count";
    public static final String DELETE_USER_MAX_COUNT = "system:delete:user:max:count";
    public static final String REREGISTER_TIME_INTERVAL = "system:reregister:time:interval";// 注销后再次注册间隔
    public static final String OUT_GOODS_MIN_COUNT = "system:out:goods:min:count";// tab中本地生活和全球购是否显示的商品数阈值

    public static final String REGION_DATA_ALL = "region:data:all";
    /**
     * ================全站通用配置 end===================
     */

    /**
     * ================用户购买统计 begin===================
     */
    // 用户当月产品spu已购金额 order:purchased:yyyyMM:用户ID
    // Hash结构: field为productType_spuId, value为已购金额
    public static final String ORDER_PURCHASED_MONTHLY = "order:purchased:%s:%d";

    // 用户当月产品sku已购数量 order:purchased:quantity:yyyyMM:用户ID
    // Hash结构: field为productType_spuId_skuId, value为已购数量
    public static final String ORDER_PURCHASED_MONTHLY_QUANTITY = "order:purchased:quantity:%s:%d";
    /**
     * ================用户购买统计 end===================
     */

    /**
     * ================ 用户缓存 begin ===================
     */
    // 平台默认支付顺序
    public static final String DEFAULT_PAY_SORT = "default:pay:sort";

    // 用户支付顺序缓存 customer:pay:sort:customerId
    public static final String CUSTOMER_PAY_SORT = "customer:pay:sort:";

    // 微信小程序登录session_key  wechat:min:session_key:openId 缓存有效期2小时
    public static final String WECHAT_MIN_SESSION_KEY = "wechat:min:session_key:";

    // 微信app授权登录access_token  wechat:app:access_token:openId 缓存有效期2小时
    public static final String WECHAT_APP_ACCESS_TOKEN = "wechat:app:access_token:";

    // 用户信息缓存-unionid    customer:info:unionid:{unionid}
    public static final String CUSTOMER_INFO_UNIONID = "customer:info:unionid:";

    // 用户信息缓存-customerId    customer:info:{customerId}
    public static final String CUSTOMER_INFO = "customer:info:";

    // 用户验证码缓存 customer:verification:code:phone
    public static final String CUSTOMER_VERIFICATION_CODE = "customer:verification:code:";

    // 用户每天查询卡余额次数缓存 card:balance:daily:query:count:customerId
    public static final String CARD_BALANCE_DAILY_QUERY_COUNT = "card:balance:daily:query:count:";

    /**
     * ================ 用户缓存 end ===================
     */

    /**
     * ================ 优惠券 begin ===================
     */
    public static final String PRODUCT_SKU_TICKET = "product:sku:ticket:%s:%s";

    public static final String ORDER_GIFT_TICKET_KEY = "order:ticket:";
    /**
     * ================ 优惠券 end ===================
     */

    /**
     * ================ 扫码付 websocket begin ===================
     */
    // 扫码付用户缓存 wss:scan:customer:{customerId}:{payCode}
    public static final String WSS_SCAN_CUSTOMER = "wss:scan:customer:%s:%s";
    /**
     * ================ 扫码付 websocket end ===================
     */

    /**
     * ================ 帮助中心 begin ===================
     */
    public static final String HELP_CAT_LIST = "help:cat:list";
    /**
     * ================ 帮助中心 end ===================
     */
}
